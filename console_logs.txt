Updating node AgenticAI-1750938129348 with new config for model_provider: cline
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'model_provider', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'base_url', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticA<PERSON>', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'api_key', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'model_name', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'temperature', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'description', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'execution_type', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'query', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'system_message', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'termination_condition', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'max_tokens', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'input_variables', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'tools', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'memory', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'autogen_agent_type', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'model_provider', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'base_url', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'api_key', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'model_name', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'temperature', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'description', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'execution_type', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'query', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'system_message', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'termination_condition', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'max_tokens', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'input_variables', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'tools', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'memory', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'autogen_agent_type', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
fieldValidation.ts:565 [2025-06-26 14:06:12] [isFieldRequired] Checking if field is required:
    - Node: AgenticAI-1750938129348 (AI Agent Executor)
    - Field: model_provider (Model Provider)
    - Input type: dropdown
    - Explicitly required: NO
    - Is handle: NO
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:541 [2025-06-26 14:06:12] [isMCPMarketplaceComponent] Node AgenticAI-1750938129348 (AI Agent Executor):
    - type: agent
    - originalType: AgenticAI
    - definition.type: component
    - has mcp_info: false
    - path: components.ai.agenticai
    - RESULT: NOT MCP COMPONENT
fieldValidation.ts:647 [2025-06-26 14:06:12] [isFieldRequired] Standard component field model_provider required status: OPTIONAL (required !== false: false)
fieldValidation.ts:565 [2025-06-26 14:06:12] [isFieldRequired] Checking if field is required:
    - Node: AgenticAI-1750938129348 (AI Agent Executor)
    - Field: model_provider (Model Provider)
    - Input type: dropdown
    - Explicitly required: NO
    - Is handle: NO
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:541 [2025-06-26 14:06:12] [isMCPMarketplaceComponent] Node AgenticAI-1750938129348 (AI Agent Executor):
    - type: agent
    - originalType: AgenticAI
    - definition.type: component
    - has mcp_info: false
    - path: components.ai.agenticai
    - RESULT: NOT MCP COMPONENT
fieldValidation.ts:647 [2025-06-26 14:06:12] [isFieldRequired] Standard component field model_provider required status: OPTIONAL (required !== false: false)
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'dropdown', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'model_provider'}
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'dropdown', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'model_provider'}
VM129 src_components_inspector_be6196a1._.js:208 🔍 Model provider dropdown options: (20) ['alibaba', 'anthropic', 'cline', 'coding', 'deepinfra', 'deepseek', 'google', 'groq', 'minimaxi', 'mistral', 'nebius', 'netmind', 'novita', 'openai', 'parasail', 'perplexity', 'together', 'vertex', 'xai', 'Custom']
VM129 src_components_inspector_be6196a1._.js:208 🔍 Model provider dropdown options: (20) ['alibaba', 'anthropic', 'cline', 'coding', 'deepinfra', 'deepseek', 'google', 'groq', 'minimaxi', 'mistral', 'nebius', 'netmind', 'novita', 'openai', 'parasail', 'perplexity', 'together', 'vertex', 'xai', 'Custom']
fieldValidation.ts:565 [2025-06-26 14:06:12] [isFieldRequired] Checking if field is required:
    - Node: AgenticAI-1750938129348 (AI Agent Executor)
    - Field: api_key (API Key)
    - Input type: credential
    - Explicitly required: NO
    - Is handle: NO
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:541 [2025-06-26 14:06:12] [isMCPMarketplaceComponent] Node AgenticAI-1750938129348 (AI Agent Executor):
    - type: agent
    - originalType: AgenticAI
    - definition.type: component
    - has mcp_info: false
    - path: components.ai.agenticai
    - RESULT: NOT MCP COMPONENT
fieldValidation.ts:647 [2025-06-26 14:06:12] [isFieldRequired] Standard component field api_key required status: OPTIONAL (required !== false: false)
fieldValidation.ts:565 [2025-06-26 14:06:12] [isFieldRequired] Checking if field is required:
    - Node: AgenticAI-1750938129348 (AI Agent Executor)
    - Field: api_key (API Key)
    - Input type: credential
    - Explicitly required: NO
    - Is handle: NO
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:541 [2025-06-26 14:06:12] [isMCPMarketplaceComponent] Node AgenticAI-1750938129348 (AI Agent Executor):
    - type: agent
    - originalType: AgenticAI
    - definition.type: component
    - has mcp_info: false
    - path: components.ai.agenticai
    - RESULT: NOT MCP COMPONENT
fieldValidation.ts:647 [2025-06-26 14:06:12] [isFieldRequired] Standard component field api_key required status: OPTIONAL (required !== false: false)
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'credential', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'api_key'}
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'credential', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'api_key'}
fieldValidation.ts:565 [2025-06-26 14:06:12] [isFieldRequired] Checking if field is required:
    - Node: AgenticAI-1750938129348 (AI Agent Executor)
    - Field: model_name (Model)
    - Input type: dropdown
    - Explicitly required: NO
    - Is handle: NO
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:541 [2025-06-26 14:06:12] [isMCPMarketplaceComponent] Node AgenticAI-1750938129348 (AI Agent Executor):
    - type: agent
    - originalType: AgenticAI
    - definition.type: component
    - has mcp_info: false
    - path: components.ai.agenticai
    - RESULT: NOT MCP COMPONENT
fieldValidation.ts:647 [2025-06-26 14:06:12] [isFieldRequired] Standard component field model_name required status: OPTIONAL (required !== false: false)
fieldValidation.ts:565 [2025-06-26 14:06:12] [isFieldRequired] Checking if field is required:
    - Node: AgenticAI-1750938129348 (AI Agent Executor)
    - Field: model_name (Model)
    - Input type: dropdown
    - Explicitly required: NO
    - Is handle: NO
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:541 [2025-06-26 14:06:12] [isMCPMarketplaceComponent] Node AgenticAI-1750938129348 (AI Agent Executor):
    - type: agent
    - originalType: AgenticAI
    - definition.type: component
    - has mcp_info: false
    - path: components.ai.agenticai
    - RESULT: NOT MCP COMPONENT
fieldValidation.ts:647 [2025-06-26 14:06:12] [isFieldRequired] Standard component field model_name required status: OPTIONAL (required !== false: false)
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'dropdown', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'model_name'}
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'dropdown', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'model_name'}
fieldValidation.ts:565 [2025-06-26 14:06:12] [isFieldRequired] Checking if field is required:
    - Node: AgenticAI-1750938129348 (AI Agent Executor)
    - Field: temperature (Temperature)
    - Input type: float
    - Explicitly required: NO
    - Is handle: NO
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:541 [2025-06-26 14:06:12] [isMCPMarketplaceComponent] Node AgenticAI-1750938129348 (AI Agent Executor):
    - type: agent
    - originalType: AgenticAI
    - definition.type: component
    - has mcp_info: false
    - path: components.ai.agenticai
    - RESULT: NOT MCP COMPONENT
fieldValidation.ts:647 [2025-06-26 14:06:12] [isFieldRequired] Standard component field temperature required status: OPTIONAL (required !== false: false)
fieldValidation.ts:565 [2025-06-26 14:06:12] [isFieldRequired] Checking if field is required:
    - Node: AgenticAI-1750938129348 (AI Agent Executor)
    - Field: temperature (Temperature)
    - Input type: float
    - Explicitly required: NO
    - Is handle: NO
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:541 [2025-06-26 14:06:12] [isMCPMarketplaceComponent] Node AgenticAI-1750938129348 (AI Agent Executor):
    - type: agent
    - originalType: AgenticAI
    - definition.type: component
    - has mcp_info: false
    - path: components.ai.agenticai
    - RESULT: NOT MCP COMPONENT
fieldValidation.ts:647 [2025-06-26 14:06:12] [isFieldRequired] Standard component field temperature required status: OPTIONAL (required !== false: false)
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'float', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'temperature'}
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'float', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'temperature'}
fieldValidation.ts:565 [2025-06-26 14:06:12] [isFieldRequired] Checking if field is required:
    - Node: AgenticAI-1750938129348 (AI Agent Executor)
    - Field: description (Description)
    - Input type: string
    - Explicitly required: NO
    - Is handle: NO
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:541 [2025-06-26 14:06:12] [isMCPMarketplaceComponent] Node AgenticAI-1750938129348 (AI Agent Executor):
    - type: agent
    - originalType: AgenticAI
    - definition.type: component
    - has mcp_info: false
    - path: components.ai.agenticai
    - RESULT: NOT MCP COMPONENT
fieldValidation.ts:647 [2025-06-26 14:06:12] [isFieldRequired] Standard component field description required status: OPTIONAL (required !== false: false)
fieldValidation.ts:565 [2025-06-26 14:06:12] [isFieldRequired] Checking if field is required:
    - Node: AgenticAI-1750938129348 (AI Agent Executor)
    - Field: description (Description)
    - Input type: string
    - Explicitly required: NO
    - Is handle: NO
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:541 [2025-06-26 14:06:12] [isMCPMarketplaceComponent] Node AgenticAI-1750938129348 (AI Agent Executor):
    - type: agent
    - originalType: AgenticAI
    - definition.type: component
    - has mcp_info: false
    - path: components.ai.agenticai
    - RESULT: NOT MCP COMPONENT
fieldValidation.ts:647 [2025-06-26 14:06:12] [isFieldRequired] Standard component field description required status: OPTIONAL (required !== false: false)
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'string', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'description'}
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'string', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'description'}
fieldValidation.ts:565 [2025-06-26 14:06:12] [isFieldRequired] Checking if field is required:
    - Node: AgenticAI-1750938129348 (AI Agent Executor)
    - Field: execution_type (Execution Type)
    - Input type: dropdown
    - Explicitly required: NO
    - Is handle: NO
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:541 [2025-06-26 14:06:12] [isMCPMarketplaceComponent] Node AgenticAI-1750938129348 (AI Agent Executor):
    - type: agent
    - originalType: AgenticAI
    - definition.type: component
    - has mcp_info: false
    - path: components.ai.agenticai
    - RESULT: NOT MCP COMPONENT
fieldValidation.ts:647 [2025-06-26 14:06:12] [isFieldRequired] Standard component field execution_type required status: OPTIONAL (required !== false: false)
fieldValidation.ts:565 [2025-06-26 14:06:12] [isFieldRequired] Checking if field is required:
    - Node: AgenticAI-1750938129348 (AI Agent Executor)
    - Field: execution_type (Execution Type)
    - Input type: dropdown
    - Explicitly required: NO
    - Is handle: NO
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:541 [2025-06-26 14:06:12] [isMCPMarketplaceComponent] Node AgenticAI-1750938129348 (AI Agent Executor):
    - type: agent
    - originalType: AgenticAI
    - definition.type: component
    - has mcp_info: false
    - path: components.ai.agenticai
    - RESULT: NOT MCP COMPONENT
fieldValidation.ts:647 [2025-06-26 14:06:12] [isFieldRequired] Standard component field execution_type required status: OPTIONAL (required !== false: false)
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'dropdown', componentType: '
[Input System Migration] Dynamic system used {inputType: 'dropdown', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'execution_type'}
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'dropdown', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'execution_type'}
fieldValidation.ts:565 [2025-06-26 14:06:12] [isFieldRequired] Checking if field is required:
    - Node: AgenticAI-1750938129348 (AI Agent Executor)
    - Field: query (Query/Objective)
    - Input type: string
    - Explicitly required: YES
    - Is handle: YES
    - Is handle connected: YES
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:607 [2025-06-26 14:06:12] [isFieldRequired] Field query is a handle input and is connected, not required for direct input
fieldValidation.ts:565 [2025-06-26 14:06:12] [isFieldRequired] Checking if field is required:
    - Node: AgenticAI-1750938129348 (AI Agent Executor)
    - Field: query (Query/Objective)
    - Input type: string
    - Explicitly required: YES
    - Is handle: YES
    - Is handle connected: YES
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:607 [2025-06-26 14:06:12] [isFieldRequired] Field query is a handle input and is connected, not required for direct input
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'string', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'query'}
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'string', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'query'}
fieldValidation.ts:565 [2025-06-26 14:06:12] [isFieldRequired] Checking if field is required:
    - Node: AgenticAI-1750938129348 (AI Agent Executor)
    - Field: system_message (System Message)
    - Input type: string
    - Explicitly required: NO
    - Is handle: NO
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:541 [2025-06-26 14:06:12] [isMCPMarketplaceComponent] Node AgenticAI-1750938129348 (AI Agent Executor):
    - type: agent
    - originalType: AgenticAI
    - definition.type: component
    - has mcp_info: false
    - path: components.ai.agenticai
    - RESULT: NOT MCP COMPONENT
fieldValidation.ts:647 [2025-06-26 14:06:12] [isFieldRequired] Standard component field system_message required status: OPTIONAL (required !== false: false)
fieldValidation.ts:565 [2025-06-26 14:06:12] [isFieldRequired] Checking if field is required:
    - Node: AgenticAI-1750938129348 (AI Agent Executor)
    - Field: system_message (System Message)
    - Input type: string
    - Explicitly required: NO
    - Is handle: NO
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:541 [2025-06-26 14:06:12] [isMCPMarketplaceComponent] Node AgenticAI-1750938129348 (AI Agent Executor):
    - type: agent
    - originalType: AgenticAI
    - definition.type: component
    - has mcp_info: false
    - path: components.ai.agenticai
    - RESULT: NOT MCP COMPONENT
fieldValidation.ts:647 [2025-06-26 14:06:12] [isFieldRequired] Standard component field system_message required status: OPTIONAL (required !== false: false)
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'string', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'system_message'}
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'string', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'system_message'}
fieldValidation.ts:565 [2025-06-26 14:06:12] [isFieldRequired] Checking if field is required:
    - Node: AgenticAI-1750938129348 (AI Agent Executor)
    - Field: max_tokens (Max Tokens)
    - Input type: int
    - Explicitly required: NO
    - Is handle: NO
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:541 [2025-06-26 14:06:12] [isMCPMarketplaceComponent] Node AgenticAI-1750938129348 (AI Agent Executor):
    - type: agent
    - originalType: AgenticAI
    - definition.type: component
    - has mcp_info: false
    - path: components.ai.agenticai
    - RESULT: NOT MCP COMPONENT
fieldValidation.ts:647 [2025-06-26 14:06:12] [isFieldRequired] Standard component field max_tokens required status: OPTIONAL (required !== false: false)
fieldValidation.ts:565 [2025-06-26 14:06:12] [isFieldRequired] Checking if field is required:
    - Node: AgenticAI-1750938129348 (AI Agent Executor)
    - Field: max_tokens (Max Tokens)
    - Input type: int
    - Explicitly required: NO
    - Is handle: NO
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:541 [2025-06-26 14:06:12] [isMCPMarketplaceComponent] Node AgenticAI-1750938129348 (AI Agent Executor):
    - type: agent
    - originalType: AgenticAI
    - definition.type: component
    - has mcp_info: false
    - path: components.ai.agenticai
    - RESULT: NOT MCP COMPONENT
fieldValidation.ts:647 [2025-06-26 14:06:12] [isFieldRequired] Standard component field max_tokens required status: OPTIONAL (required !== false: false)
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'int', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'max_tokens'}
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'int', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'max_tokens'}
fieldValidation.ts:565 [2025-06-26 14:06:12] [isFieldRequired] Checking if field is required:
    - Node: AgenticAI-1750938129348 (AI Agent Executor)
    - Field: input_variables (Input Variables)
    - Input type: dict
    - Explicitly required: NO
    - Is handle: YES
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:621 [2025-06-26 14:06:12] [isFieldRequired] Field input_variables is a handle input and not explicitly required, not required for direct input
fieldValidation.ts:565 [2025-06-26 14:06:12] [isFieldRequired] Checking if field is required:
    - Node: AgenticAI-1750938129348 (AI Agent Executor)
    - Field: input_variables (Input Variables)
    - Input type: dict
    - Explicitly required: NO
    - Is handle: YES
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:621 [2025-06-26 14:06:12] [isFieldRequired] Field input_variables is a handle input and not explicitly required, not required for direct input
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'dict', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'input_variables'}
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'dict', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'input_variables'}
fieldValidation.ts:565 [2025-06-26 14:06:12] [isFieldRequired] Checking if field is required:
    - Node: AgenticAI-1750938129348 (AI Agent Executor)
    - Field: tools (Tools)
    - Input type: handle
    - Explicitly required: NO
    - Is handle: YES
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:621 [2025-06-26 14:06:12] [isFieldRequired] Field tools is a handle input and not explicitly required, not required for direct input
fieldValidation.ts:565 [2025-06-26 14:06:12] [isFieldRequired] Checking if field is required:
    - Node: AgenticAI-1750938129348 (AI Agent Executor)
    - Field: tools (Tools)
    - Input type: handle
    - Explicitly required: NO
    - Is handle: YES
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:621 [2025-06-26 14:06:12] [isFieldRequired] Field tools is a handle input and not explicitly required, not required for direct input
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'handle', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'tools'}
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'handle', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'tools'}
fieldValidation.ts:565 [2025-06-26 14:06:12] [isFieldRequired] Checking if field is required:
    - Node: AgenticAI-1750938129348 (AI Agent Executor)
    - Field: memory (Memory Object)
    - Input type: handle
    - Explicitly required: NO
    - Is handle: YES
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:621 [2025-06-26 14:06:12] [isFieldRequired] Field memory is a handle input and not explicitly required, not required for direct input
fieldValidation.ts:565 [2025-06-26 14:06:12] [isFieldRequired] Checking if field is required:
    - Node: AgenticAI-1750938129348 (AI Agent Executor)
    - Field: memory (Memory Object)
    - Input type: handle
    - Explicitly required: NO
    - Is handle: YES
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:621 [2025-06-26 14:06:12] [isFieldRequired] Field memory is a handle input and not explicitly required, not required for direct input
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'handle', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'memory'}
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'handle', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'memory'}
fieldValidation.ts:565 [2025-06-26 14:06:12] [isFieldRequired] Checking if field is required:
    - Node: AgenticAI-1750938129348 (AI Agent Executor)
    - Field: autogen_agent_type (AutoGen Agent Type)
    - Input type: dropdown
    - Explicitly required: NO
    - Is handle: NO
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:541 [2025-06-26 14:06:12] [isMCPMarketplaceComponent] Node AgenticAI-1750938129348 (AI Agent Executor):
    - type: agent
    - originalType: AgenticAI
    - definition.type: component
    - has mcp_info: false
    - path: components.ai.agenticai
    - RESULT: NOT MCP COMPONENT
fieldValidation.ts:647 [2025-06-26 14:06:12] [isFieldRequired] Standard component field autogen_agent_type required status: OPTIONAL (required !== false: false)
fieldValidation.ts:565 [2025-06-26 14:06:12] [isFieldRequired] Checking if field is required:
    - Node: AgenticAI-1750938129348 (AI Agent Executor)
    - Field: autogen_agent_type (AutoGen Agent Type)
    - Input type: dropdown
    - Explicitly required: NO
    - Is handle: NO
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:541 [2025-06-26 14:06:12] [isMCPMarketplaceComponent] Node AgenticAI-1750938129348 (AI Agent Executor):
    - type: agent
    - originalType: AgenticAI
    - definition.type: component
    - has mcp_info: false
    - path: components.ai.agenticai
    - RESULT: NOT MCP COMPONENT
fieldValidation.ts:647 [2025-06-26 14:06:12] [isFieldRequired] Standard component field autogen_agent_type required status: OPTIONAL (required !== false: false)
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'dropdown', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'autogen_agent_type'}
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'dropdown', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'autogen_agent_type'}
RunButton.tsx:56 Validation of currentw workflow before execution async (providedNodes, providedEdges)=>{
            // Use provided nodes/edges if available, otherwise get from React Flow
            const nodes = providedNodes || getNodes();
            const ed…
RunButton.tsx:56 Validation of currentw workflow before execution async (providedNodes, providedEdges)=>{
            // Use provided nodes/edges if available, otherwise get from React Flow
            const nodes = providedNodes || getNodes();
            const ed…
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'model_provider', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'base_url', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'api_key', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'model_name', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'temperature', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'description', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'execution_type', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'query', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'system_message', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'termination_condition', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'max_tokens', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'input_variables', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'tools', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'memory', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'autogen_agent_type', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'model_provider', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'base_url', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'api_key', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'model_name', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'temperature', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'description', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'execution_type', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'query', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'system_message', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'termination_condition', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'max_tokens', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'input_variables', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'tools', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'memory', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
inputVisibility.ts:80 🚨 [MERGE-DEBUG] Component check: {inputName: 'autogen_agent_type', isMergeDataComponent: false, isOutputKey: false, nodeType: 'agent', originalType: 'AgenticAI', …}
fieldValidation.ts:565 [2025-06-26 14:06:12] [isFieldRequired] Checking if field is required:
    - Node: AgenticAI-1750938129348 (AI Agent Executor)
    - Field: model_provider (Model Provider)
    - Input type: dropdown
    - Explicitly required: NO
    - Is handle: NO
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:541 [2025-06-26 14:06:12] [isMCPMarketplaceComponent] Node AgenticAI-1750938129348 (AI Agent Executor):
    - type: agent
    - originalType: AgenticAI
    - definition.type: component
    - has mcp_info: false
    - path: components.ai.agenticai
    - RESULT: NOT MCP COMPONENT
fieldValidation.ts:647 [2025-06-26 14:06:12] [isFieldRequired] Standard component field model_provider required status: OPTIONAL (required !== false: false)
fieldValidation.ts:565 [2025-06-26 14:06:12] [isFieldRequired] Checking if field is required:
    - Node: AgenticAI-1750938129348 (AI Agent Executor)
    - Field: model_provider (Model Provider)
    - Input type: dropdown
    - Explicitly required: NO
    - Is handle: NO
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:541 [2025-06-26 14:06:12] [isMCPMarketplaceComponent] Node AgenticAI-1750938129348 (AI Agent Executor):
    - type: agent
    - originalType: AgenticAI
    - definition.type: component
    - has mcp_info: false
    - path: components.ai.agenticai
    - RESULT: NOT MCP COMPONENT
fieldValidation.ts:647 [2025-06-26 14:06:12] [isFieldRequired] Standard component field model_provider required status: OPTIONAL (required !== false: false)
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'dropdown', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'model_provider'}
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'dropdown', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'model_provider'}
VM129 src_components_inspector_be6196a1._.js:208 🔍 Model provider dropdown options: (20) ['alibaba', 'anthropic', 'cline', 'coding', 'deepinfra', 'deepseek', 'google', 'groq', 'minimaxi', 'mistral', 'nebius', 'netmind', 'novita', 'openai', 'parasail', 'perplexity', 'together', 'vertex', 'xai', 'Custom']
VM129 src_components_inspector_be6196a1._.js:208 🔍 Model provider dropdown options: (20) ['alibaba', 'anthropic', 'cline', 'coding', 'deepinfra', 'deepseek', 'google', 'groq', 'minimaxi', 'mistral', 'nebius', 'netmind', 'novita', 'openai', 'parasail', 'perplexity', 'together', 'vertex', 'xai', 'Custom']
fieldValidation.ts:565 [2025-06-26 14:06:12] [isFieldRequired] Checking if field is required:
    - Node: AgenticAI-1750938129348 (AI Agent Executor)
    - Field: api_key (API Key)
    - Input type: credential
    - Explicitly required: NO
    - Is handle: NO
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:541 [2025-06-26 14:06:12] [isMCPMarketplaceComponent] Node AgenticAI-1750938129348 (AI Agent Executor):
    - type: agent
    - originalType: AgenticAI
    - definition.type: component
    - has mcp_info: false
    - path: components.ai.agenticai
    - RESULT: NOT MCP COMPONENT
fieldValidation.ts:647 [2025-06-26 14:06:12] [isFieldRequired] Standard component field api_key required status: OPTIONAL (required !== false: false)
fieldValidation.ts:565 [2025-06-26 14:06:12] [isFieldRequired] Checking if field is required:
    - Node: AgenticAI-1750938129348 (AI Agent Executor)
    - Field: api_key (API Key)
    - Input type: credential
    - Explicitly required: NO
    - Is handle: NO
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:541 [2025-06-26 14:06:12] [isMCPMarketplaceComponent] Node AgenticAI-1750938129348 (AI Agent Executor):
    - type: agent
    - originalType: AgenticAI
    - definition.type: component
    - has mcp_info: false
    - path: components.ai.agenticai
    - RESULT: NOT MCP COMPONENT
fieldValidation.ts:647 [2025-06-26 14:06:12] [isFieldRequired] Standard component field api_key required status: OPTIONAL (required !== false: false)
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'credential', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'api_key'}
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'credential', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'api_key'}
fieldValidation.ts:565 [2025-06-26 14:06:12] [isFieldRequired] Checking if field is required:
    - Node: AgenticAI-1750938129348 (AI Agent Executor)
    - Field: model_name (Model)
    - Input type: dropdown
    - Explicitly required: NO
    - Is handle: NO
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:541 [2025-06-26 14:06:12] [isMCPMarketplaceComponent] Node AgenticAI-1750938129348 (AI Agent Executor):
    - type: agent
    - originalType: AgenticAI
    - definition.type: component
    - has mcp_info: false
    - path: components.ai.agenticai
    - RESULT: NOT MCP COMPONENT
fieldValidation.ts:647 [2025-06-26 14:06:12] [isFieldRequired] Standard component field model_name required status: OPTIONAL (required !== false: false)
fieldValidation.ts:565 [2025-06-26 14:06:12] [isFieldRequired] Checking if field is required:
    - Node: AgenticAI-1750938129348 (AI Agent Executor)
    - Field: model_name (Model)
    - Input type: dropdown
    - Explicitly required: NO
    - Is handle: NO
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:541 [2025-06-26 14:06:12] [isMCPMarketplaceComponent] Node AgenticAI-1750938129348 (AI Agent Executor):
    - type: agent
    - originalType: AgenticAI
    - definition.type: component
    - has mcp_info: false
    - path: components.ai.agenticai
    - RESULT: NOT MCP COMPONENT
fieldValidation.ts:647 [2025-06-26 14:06:12] [isFieldRequired] Standard component field model_name required status: OPTIONAL (required !== false: false)
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'dropdown', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'model_name'}
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'dropdown', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'model_name'}
fieldValidation.ts:565 [2025-06-26 14:06:12] [isFieldRequired] Checking if field is required:
    - Node: AgenticAI-1750938129348 (AI Agent Executor)
    - Field: temperature (Temperature)
    - Input type: float
    - Explicitly required: NO
    - Is handle: NO
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:541 [2025-06-26 14:06:12] [isMCPMarketplaceComponent] Node AgenticAI-1750938129348 (AI Agent Executor):
    - type: agent
    - originalType: AgenticAI
    - definition.type: component
    - has mcp_info: false
    - path: components.ai.agenticai
    - RESULT: NOT MCP COMPONENT
fieldValidation.ts:647 [2025-06-26 14:06:12] [isFieldRequired] Standard component field temperature required status: OPTIONAL (required !== false: false)
fieldValidation.ts:565 [2025-06-26 14:06:12] [isFieldRequired] Checking if field is required:
    - Node: AgenticAI-1750938129348 (AI Agent Executor)
    - Field: temperature (Temperature)
    - Input type: float
    - Explicitly required: NO
    - Is handle: NO
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:541 [2025-06-26 14:06:12] [isMCPMarketplaceComponent] Node AgenticAI-1750938129348 (AI Agent Executor):
    - type: agent
    - originalType: AgenticAI
    - definition.type: component
    - has mcp_info: false
    - path: components.ai.agenticai
    - RESULT: NOT MCP COMPONENT
fieldValidation.ts:647 [2025-06-26 14:06:12] [isFieldRequired] Standard component field temperature required status: OPTIONAL (required !== false: false)
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'float', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'temperature'}
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'float', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'temperature'}
fieldValidation.ts:565 [2025-06-26 14:06:12] [isFieldRequired] Checking if field is required:
    - Node: AgenticAI-1750938129348 (AI Agent Executor)
    - Field: description (Description)
    - Input type: string
    - Explicitly required: NO
    - Is handle: NO
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:541 [2025-06-26 14:06:12] [isMCPMarketplaceComponent] Node AgenticAI-1750938129348 (AI Agent Executor):
    - type: agent
    - originalType: AgenticAI
    - definition.type: component
    - has mcp_info: false
    - path: components.ai.agenticai
    - RESULT: NOT MCP COMPONENT
    [2025-06-26 14:06:12] [isFieldRequired] Standard component field description required status: OPTIONAL (required !== false: false)
fieldValidation.ts:565 [2025-06-26 14:06:12] [isFieldRequired] Checking if field is required:
    - Node: AgenticAI-1750938129348 (AI Agent Executor)
    - Field: description (Description)
    - Input type: string
    - Explicitly required: NO
    - Is handle: NO
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:541 [2025-06-26 14:06:12] [isMCPMarketplaceComponent] Node AgenticAI-1750938129348 (AI Agent Executor):
    - type: agent
    - originalType: AgenticAI
    - definition.type: component
    - has mcp_info: false
    - path: components.ai.agenticai
    - RESULT: NOT MCP COMPONENT
fieldValidation.ts:647 [2025-06-26 14:06:12] [isFieldRequired] Standard component field description required status: OPTIONAL (required !== false: false)
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'string', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'description'}
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'string', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'description'}
fieldValidation.ts:565 [2025-06-26 14:06:12] [isFieldRequired] Checking if field is required:
    - Node: AgenticAI-1750938129348 (AI Agent Executor)
    - Field: execution_type (Execution Type)
    - Input type: dropdown
    - Explicitly required: NO
    - Is handle: NO
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:541 [2025-06-26 14:06:12] [isMCPMarketplaceComponent] Node AgenticAI-1750938129348 (AI Agent Executor):
    - type: agent
    - originalType: AgenticAI
    - definition.type: component
    - has mcp_info: false
    - path: components.ai.agenticai
    - RESULT: NOT MCP COMPONENT
fieldValidation.ts:647 [2025-06-26 14:06:12] [isFieldRequired] Standard component field execution_type required status: OPTIONAL (required !== false: false)
fieldValidation.ts:565 [2025-06-26 14:06:12] [isFieldRequired] Checking if field is required:
    - Node: AgenticAI-1750938129348 (AI Agent Executor)
    - Field: execution_type (Execution Type)
    - Input type: dropdown
    - Explicitly required: NO
    - Is handle: NO
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:541 [2025-06-26 14:06:12] [isMCPMarketplaceComponent] Node AgenticAI-1750938129348 (AI Agent Executor):
    - type: agent
    - originalType: AgenticAI
    - definition.type: component
    - has mcp_info: false
    - path: components.ai.agenticai
    - RESULT: NOT MCP COMPONENT
fieldValidation.ts:647 [2025-06-26 14:06:12] [isFieldRequired] Standard component field execution_type required status: OPTIONAL (required !== false: false)
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'dropdown', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'execution_type'}
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'dropdown', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'execution_type'}
fieldValidation.ts:565 [2025-06-26 14:06:12] [isFieldRequired] Checking if field is required:
    - Node: AgenticAI-1750938129348 (AI Agent Executor)
    - Field: query (Query/Objective)
    - Input type: string
    - Explicitly required: YES
    - Is handle: YES
    - Is handle connected: YES
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:607 [2025-06-26 14:06:12] [isFieldRequired] Field query is a handle input and is connected, not required for direct input
fieldValidation.ts:565 [2025-06-26 14:06:12] [isFieldRequired] Checking if field is required:
    - Node: AgenticAI-1750938129348 (AI Agent Executor)
    - Field: query (Query/Objective)
    - Input type: string
    - Explicitly required: YES
    - Is handle: YES
    - Is handle connected: YES
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:607 [2025-06-26 14:06:12] [isFieldRequired] Field query is a handle input and is connected, not required for direct input
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'string', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'query'}
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'string', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'query'}
fieldValidation.ts:565 [2025-06-26 14:06:12] [isFieldRequired] Checking if field is required:
    - Node: AgenticAI-1750938129348 (AI Agent Executor)
    - Field: system_message (System Message)
    - Input type: string
    - Explicitly required: NO
    - Is handle: NO
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:541 [2025-06-26 14:06:12] [isMCPMarketplaceComponent] Node AgenticAI-1750938129348 (AI Agent Executor):
    - type: agent
    - originalType: AgenticAI
    - definition.type: component
    - has mcp_info: false
    - path: components.ai.agenticai
    - RESULT: NOT MCP COMPONENT
fieldValidation.ts:647 [2025-06-26 14:06:12] [isFieldRequired] Standard component field system_message required status: OPTIONAL (required !== false: false)
fieldValidation.ts:565 [2025-06-26 14:06:12] [isFieldRequired] Checking if field is required:
    - Node: AgenticAI-1750938129348 (AI Agent Executor)
    - Field: system_message (System Message)
    - Input type: string
    - Explicitly required: NO
    - Is handle: NO
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:541 [2025-06-26 14:06:12] [isMCPMarketplaceComponent] Node AgenticAI-1750938129348 (AI Agent Executor):
    - type: agent
    - originalType: AgenticAI
    - definition.type: component
    - has mcp_info: false
    - path: components.ai.agenticai
    - RESULT: NOT MCP COMPONENT
fieldValidation.ts:647 [2025-06-26 14:06:12] [isFieldRequired] Standard component field system_message required status: OPTIONAL (required !== false: false)
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'string', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'system_message'}
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'string', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'system_message'}
fieldValidation.ts:565 [2025-06-26 14:06:12] [isFieldRequired] Checking if field is required:
    - Node: AgenticAI-1750938129348 (AI Agent Executor)
    - Field: max_tokens (Max Tokens)
    - Input type: int
    - Explicitly required: NO
    - Is handle: NO
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:541 [2025-06-26 14:06:12] [isMCPMarketplaceComponent] Node AgenticAI-1750938129348 (AI Agent Executor):
    - type: agent
    - originalType: AgenticAI
    - definition.type: component
    - has mcp_info: false
    - path: components.ai.agenticai
    - RESULT: NOT MCP COMPONENT
fieldValidation.ts:647 [2025-06-26 14:06:12] [isFieldRequired] Standard component field max_tokens required status: OPTIONAL (required !== false: false)
fieldValidation.ts:565 [2025-06-26 14:06:12] [isFieldRequired] Checking if field is required:
    - Node: AgenticAI-1750938129348 (AI Agent Executor)
    - Field: max_tokens (Max Tokens)
    - Input type: int
    - Explicitly required: NO
    - Is handle: NO
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:541 [2025-06-26 14:06:12] [isMCPMarketplaceComponent] Node AgenticAI-1750938129348 (AI Agent Executor):
    - type: agent
    - originalType: AgenticAI
    - definition.type: component
    - has mcp_info: false
    - path: components.ai.agenticai
    - RESULT: NOT MCP COMPONENT
fieldValidation.ts:647 [2025-06-26 14:06:12] [isFieldRequired] Standard component field max_tokens required status: OPTIONAL (required !== false: false)
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'int', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'max_tokens'}
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'int', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'max_tokens'}
fieldValidation.ts:565 [2025-06-26 14:06:12] [isFieldRequired] Checking if field is required:
    - Node: AgenticAI-1750938129348 (AI Agent Executor)
    - Field: input_variables (Input Variables)
    - Input type: dict
    - Explicitly required: NO
    - Is handle: YES
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:621 [2025-06-26 14:06:12] [isFieldRequired] Field input_variables is a handle input and not explicitly required, not required for direct input
fieldValidation.ts:565 [2025-06-26 14:06:12] [isFieldRequired] Checking if field is required:
    - Node: AgenticAI-1750938129348 (AI Agent Executor)
    - Field: input_variables (Input Variables)
    - Input type: dict
    - Explicitly required: NO
    - Is handle: YES
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:621 [2025-06-26 14:06:12] [isFieldRequired] Field input_variables is a handle input and not explicitly required, not required for direct input
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'dict', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'input_variables'}
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'dict', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'input_variables'}
fieldValidation.ts:565 [2025-06-26 14:06:12] [isFieldRequired] Checking if field is required:
    - Node: AgenticAI-1750938129348 (AI Agent Executor)
    - Field: tools (Tools)
    - Input type: handle
    - Explicitly required: NO
    - Is handle: YES
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:621 [2025-06-26 14:06:12] [isFieldRequired] Field tools is a handle input and not explicitly required, not required for direct input
fieldValidation.ts:565 [2025-06-26 14:06:12] [isFieldRequired] Checking if field is required:
    - Node: AgenticAI-1750938129348 (AI Agent Executor)
    - Field: tools (Tools)
    - Input type: handle
    - Explicitly required: NO
    - Is handle: YES
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:621 [2025-06-26 14:06:12] [isFieldRequired] Field tools is a handle input and not explicitly required, not required for direct input
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'handle', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'tools'}
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'handle', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'tools'}
fieldValidation.ts:565 [2025-06-26 14:06:12] [isFieldRequired] Checking if field is required:
    - Node: AgenticAI-1750938129348 (AI Agent Executor)
    - Field: memory (Memory Object)
    - Input type: handle
    - Explicitly required: NO
    - Is handle: YES
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:621 [2025-06-26 14:06:12] [isFieldRequired] Field memory is a handle input and not explicitly required, not required for direct input
fieldValidation.ts:565 [2025-06-26 14:06:12] [isFieldRequired] Checking if field is required:
    - Node: AgenticAI-1750938129348 (AI Agent Executor)
    - Field: memory (Memory Object)
    - Input type: handle
    - Explicitly required: NO
    - Is handle: YES
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:621 [2025-06-26 14:06:12] [isFieldRequired] Field memory is a handle input and not explicitly required, not required for direct input
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'handle', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'memory'}
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'handle', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'memory'}
fieldValidation.ts:565 [2025-06-26 14:06:12] [isFieldRequired] Checking if field is required:
    - Node: AgenticAI-1750938129348 (AI Agent Executor)
    - Field: autogen_agent_type (AutoGen Agent Type)
    - Input type: dropdown
    - Explicitly required: NO
    - Is handle: NO
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:541 [2025-06-26 14:06:12] [isMCPMarketplaceComponent] Node AgenticAI-1750938129348 (AI Agent Executor):
    - type: agent
    - originalType: AgenticAI
    - definition.type: component
    - has mcp_info: false
    - path: components.ai.agenticai
    - RESULT: NOT MCP COMPONENT
fieldValidation.ts:647 [2025-06-26 14:06:12] [isFieldRequired] Standard component field autogen_agent_type required status: OPTIONAL (required !== false: false)
fieldValidation.ts:565 [2025-06-26 14:06:12] [isFieldRequired] Checking if field is required:
    - Node: AgenticAI-1750938129348 (AI Agent Executor)
    - Field: autogen_agent_type (AutoGen Agent Type)
    - Input type: dropdown
    - Explicitly required: NO
    - Is handle: NO
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:541 [2025-06-26 14:06:12] [isMCPMarketplaceComponent] Node AgenticAI-1750938129348 (AI Agent Executor):
    - type: agent
    - originalType: AgenticAI
    - definition.type: component
    - has mcp_info: false
    - path: components.ai.agenticai
    - RESULT: NOT MCP COMPONENT
fieldValidation.ts:647 [2025-06-26 14:06:12] [isFieldRequired] Standard component field autogen_agent_type required status: OPTIONAL (required !== false: false)
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'dropdown', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'autogen_agent_type'}
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'dropdown', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'autogen_agent_type'}
hot-reloader-client.tsx:371 [Fast Refresh] rebuilding
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'dropdown', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'model_provider'}
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'dropdown', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'model_provider'}
InputRenderer.tsx:213 🔍 Model provider dropdown options: (20) ['alibaba', 'anthropic', 'cline', 'coding', 'deepinfra', 'deepseek', 'google', 'groq', 'minimaxi', 'mistral', 'nebius', 'netmind', 'novita', 'openai', 'parasail', 'perplexity', 'together', 'vertex', 'xai', 'Custom']
InputRenderer.tsx:213 🔍 Model provider dropdown options: (20) ['alibaba', 'anthropic', 'cline', 'coding', 'deepinfra', 'deepseek', 'google', 'groq', 'minimaxi', 'mistral', 'nebius', 'netmind', 'novita', 'openai', 'parasail', 'perplexity', 'together', 'vertex', 'xai', 'Custom']
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'credential', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'api_key'}
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'credential', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'api_key'}
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'dropdown', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'model_name'}
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'dropdown', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'model_name'}
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'float', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'temperature'}
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'float', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'temperature'}
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'string', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'description'}
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'string', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'description'}
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'dropdown', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'execution_type'}
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'dropdown', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'execution_type'}
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'string', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'query'}
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'string', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'query'}
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'string', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'system_message'}
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'string', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'system_message'}
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'int', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'max_tokens'}
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'int', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'max_tokens'}
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'dict', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'input_variables'}
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'dict', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'input_variables'}
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'handle', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'tools'}
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'handle', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'tools'}
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'handle', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'memory'}
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'handle', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'memory'}
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'dropdown', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'autogen_agent_type'}
inputSystemConfig.ts:323 [Input System Migration] Dynamic system used {inputType: 'dropdown', componentType: 'AgenticAI', nodeId: 'AgenticAI-1750938129348', inputName: 'autogen_agent_type'}
hot-reloader-client.tsx:116 [Fast Refresh] done in 243ms