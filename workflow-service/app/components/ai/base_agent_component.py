from typing import Dict, Any, List, ClassVar, Optional
from abc import ABC, abstractmethod
import os
import asyncio
import importlib
import json
import logging

from app.components.core.base_node import BaseNode
from app.models.workflow_builder.components import (
    InputBase,
    HandleInput,
    PasswordInput,
    StringInput,
    DictInput,
    FloatInput,
    DropdownInput,
    CredentialInput,
    BoolInput,
    InputVisibilityRule,
)
from app.models.workflow_builder.components import Output


from app.utils.workflow_builder.input_helpers import create_dual_purpose_input
from app.services.model_provider_service import get_model_provider_service


class BaseAgentComponent(BaseNode, ABC):
    """
    Base class for AI components.

    This is an abstract base class that provides common functionality for AI components.
    Concrete AI components should inherit from this class and implement the build method.
    """

    # These should be overridden by subclasses
    name: ClassVar[str] = "BaseAgentComponent"
    display_name: ClassVar[str] = "Base AI Agent/Module"
    description: ClassVar[str] = "Base class for AI components."

    category: ClassVar[str] = "AI"
    icon: ClassVar[str] = "BrainCircuit"

    # Flag to indicate this is an abstract base class that shouldn't be displayed in the UI
    is_abstract: ClassVar[bool] = True

    # Add a debug print to see if this class is being loaded
    print("DEBUG: BaseAgentComponent class loaded with is_abstract =", is_abstract)

    # Static inputs - will be dynamically populated
    inputs: ClassVar[List[InputBase]] = []

    @classmethod
    async def get_dynamic_inputs(cls) -> List[InputBase]:
        """
        Get dynamically populated inputs with current provider and model data.

        Returns:
            List of input definitions with current data
        """
        try:
            service = get_model_provider_service()
            providers = await service.get_providers()
            models = await service.get_models()
        except Exception as e:
            logging.error(f"Failed to fetch dynamic data for inputs: {str(e)}")
            # Use fallback data
            providers = [
                "OpenAI", "Azure OpenAI", "Anthropic", "Claude",
                "Google", "Gemini", "Mistral", "Ollama", "Custom"
            ]
            models = [
                "gpt-4o", "gpt-4o-mini", "gpt-4-turbo", "gpt-4", "gpt-3.5-turbo",
                "claude-3-5-sonnet-20241022", "claude-3-5-haiku-20241022",
                "claude-3-opus-20240229", "gemini-1.5-pro", "gemini-1.5-flash",
                "mistral-large-latest", "llama3.2", "llama3.1"
            ]

        return [
            # Model Client Configuration
            DropdownInput(
                name="model_provider",
                display_name="Model Provider",
                options=providers,
                value="OpenAI" if "OpenAI" in providers else (providers[0] if providers else "Custom"),
                info="The AI model provider to use.",
            ),
            StringInput(
                name="base_url",
                display_name="Base URL",
                required=False,
                is_handle=False,
                value="",
                info="Base URL for the API (leave empty for default provider URL).",
                visibility_rules=[
                    InputVisibilityRule(field_name="model_provider", field_value="Custom"),
                    InputVisibilityRule(field_name="model_provider", field_value="Azure OpenAI"),
                    InputVisibilityRule(field_name="model_provider", field_value="Ollama"),
                ],
                visibility_logic="OR",
            ),
            CredentialInput(
                name="api_key",
                display_name="API Key",
                credential_type="api_key",
                info="API key for the model provider. Can be entered directly or referenced from secure storage.",
            ),
            # Model selection - dropdown with dynamically fetched options
            DropdownInput(
                name="model_name",
                display_name="Model",
                options=models,
                value=models[0] if models else "gpt-4o",
                info="Select the model to use. The list is dynamically fetched from the model provider API.",
            ),
            # Temperature - direct input in inspector
            FloatInput(
                name="temperature",
                display_name="Temperature",
                required=False,
                is_handle=False,
                value=0.7,
                info="Controls randomness: 0 is deterministic, higher values are more random.",
            ),
            # Streaming option
            BoolInput(
                name="stream",
                display_name="Stream Response",
                value=False,
                info="Enable streaming for real-time responses. Note: Streaming requires frontend support.",
            ),
            # Token counting and cost estimation
            BoolInput(
                name="enable_token_counting",
                display_name="Enable Token Counting",
                value=True,
                info="Count tokens and estimate costs for API calls.",
                advanced=True,
            ),
            # Input text - single input that can be both connected and directly edited
            create_dual_purpose_input(
                name="input_text",
                display_name="Input Text",
                input_type="string",
                required=False,
                info="Text input for the AI component. Can be connected from another node or entered directly.",
                input_types=["string", "Any"],
            ),
            # Input data - single input that can be both connected and directly edited
            create_dual_purpose_input(
                name="input_data",
                display_name="Input Data",
                input_type="dict",
                required=False,
                info="Structured data input for the AI component. Can be connected from another node or entered directly.",
                input_types=["dict", "Any"],
                value={},
            ),
        ]

    @classmethod
    def get_static_inputs(cls) -> List[InputBase]:
        """
        Get static inputs that don't require API calls.
        This is used as a fallback when dynamic inputs can't be loaded.

        Returns:
            List of static input definitions
        """
        return [
            # Model Client Configuration
            DropdownInput(
                name="model_provider",
                display_name="Model Provider",
                options=[
                    "OpenAI", "Azure OpenAI", "Anthropic", "Claude",
                    "Google", "Gemini", "Mistral", "Ollama", "Custom"
                ],
                value="OpenAI",
                info="The AI model provider to use.",
            ),
            StringInput(
                name="base_url",
                display_name="Base URL",
                required=False,
                is_handle=False,
                value="",
                info="Base URL for the API (leave empty for default provider URL).",
                visibility_rules=[
                    InputVisibilityRule(field_name="model_provider", field_value="Custom"),
                    InputVisibilityRule(field_name="model_provider", field_value="Azure OpenAI"),
                    InputVisibilityRule(field_name="model_provider", field_value="Ollama"),
                ],
                visibility_logic="OR",
            ),
            CredentialInput(
                name="api_key",
                display_name="API Key",
                credential_type="api_key",
                info="API key for the model provider. Can be entered directly or referenced from secure storage.",
            ),
            DropdownInput(
                name="model_name",
                display_name="Model",
                options=[
                    "gpt-4o", "gpt-4o-mini", "gpt-4-turbo", "gpt-4", "gpt-3.5-turbo",
                    "claude-3-5-sonnet-20241022", "claude-3-5-haiku-20241022",
                    "claude-3-opus-20240229", "gemini-1.5-pro", "gemini-1.5-flash",
                    "mistral-large-latest", "llama3.2", "llama3.1"
                ],
                value="gpt-4o",
                info="Select the model to use.",
            ),
            FloatInput(
                name="temperature",
                display_name="Temperature",
                required=False,
                is_handle=False,
                value=0.7,
                info="Controls randomness: 0 is deterministic, higher values are more random.",
            ),
            BoolInput(
                name="stream",
                display_name="Stream Response",
                value=False,
                info="Enable streaming for real-time responses. Note: Streaming requires frontend support.",
            ),
            BoolInput(
                name="enable_token_counting",
                display_name="Enable Token Counting",
                value=True,
                info="Count tokens and estimate costs for API calls.",
                advanced=True,
            ),
            create_dual_purpose_input(
                name="input_text",
                display_name="Input Text",
                input_type="string",
                required=False,
                info="Text input for the AI component. Can be connected from another node or entered directly.",
                input_types=["string", "Any"],
            ),
            create_dual_purpose_input(
                name="input_data",
                display_name="Input Data",
                input_type="dict",
                required=False,
                info="Structured data input for the AI component. Can be connected from another node or entered directly.",
                input_types=["dict", "Any"],
                value={},
            ),
        ]

    outputs: ClassVar[List[Output]] = [
        Output(name="result", display_name="Result", output_type="Any"),
        Output(name="streaming_chunk", display_name="Streaming Chunk", output_type="string"),
        Output(name="token_usage", display_name="Token Usage", output_type="dict"),
        Output(name="cost_estimate", display_name="Cost Estimate", output_type="dict"),
        Output(name="error", display_name="Error", output_type="str"),
    ]

    async def _get_providers(self) -> List[str]:
        """
        Get list of available model providers from API.

        Returns:
            List of provider names
        """
        try:
            service = get_model_provider_service()
            return await service.get_providers()
        except Exception as e:
            logging.error(f"Failed to fetch providers: {str(e)}")
            # Return fallback providers
            return [
                "OpenAI",
                "Azure OpenAI",
                "Anthropic",
                "Claude",
                "Google",
                "Gemini",
                "Mistral",
                "Ollama",
                "Custom"
            ]

    async def _get_models(self) -> List[str]:
        """
        Get list of available models from API.

        Returns:
            List of model names
        """
        try:
            service = get_model_provider_service()
            return await service.get_models()
        except Exception as e:
            logging.error(f"Failed to fetch models: {str(e)}")
            # Return fallback models
            return [
                "gpt-4o", "gpt-4o-mini", "gpt-4-turbo", "gpt-4", "gpt-3.5-turbo",
                "claude-3-5-sonnet-20241022", "claude-3-5-haiku-20241022",
                "claude-3-opus-20240229", "claude-3-sonnet-20240229",
                "gemini-1.5-pro", "gemini-1.5-flash", "gemini-pro",
                "mistral-large-latest", "mistral-medium-latest",
                "llama3.2", "llama3.1", "llama3"
            ]

    async def _get_model_options(self, provider: str) -> List[str]:
        """
        Get model options for the specified provider from API.

        Args:
            provider: The model provider name.

        Returns:
            A list of model options for the provider.
        """
        try:
            service = get_model_provider_service()
            return await service.get_models_for_provider(provider)
        except Exception as e:
            logging.error(f"Failed to fetch models for provider {provider}: {str(e)}")
            return ["custom-model"]

    def _estimate_tokens(self, text: str) -> int:
        """
        Estimate the number of tokens in the text.

        Args:
            text: The text to estimate tokens for.

        Returns:
            Estimated number of tokens.
        """
        # Simple estimation: ~4 characters per token
        return len(text) // 4

    def _estimate_cost(self, token_usage: Dict[str, int], model_name: str) -> Dict[str, float]:
        """
        Estimate the cost of the API call based on token usage.

        Args:
            token_usage: Dictionary with prompt_tokens, completion_tokens, and total_tokens.
            model_name: The name of the model used.

        Returns:
            Dictionary with cost estimates.
        """
        # Default cost rates per 1000 tokens (USD)
        cost_rates = {
            # OpenAI models
            "gpt-4o": {"input": 0.005, "output": 0.015},
            "gpt-4-turbo": {"input": 0.01, "output": 0.03},
            "gpt-4": {"input": 0.03, "output": 0.06},
            "gpt-3.5-turbo": {"input": 0.0005, "output": 0.0015},
            # Anthropic models
            "claude-3-opus": {"input": 0.015, "output": 0.075},
            "claude-3-sonnet": {"input": 0.003, "output": 0.015},
            "claude-3-haiku": {"input": 0.00025, "output": 0.00125},
            "claude-2": {"input": 0.008, "output": 0.024},
            # Google models
            "gemini-pro": {"input": 0.00025, "output": 0.0005},
            "gemini-ultra": {"input": 0.0005, "output": 0.0015},
            # Mistral models
            "mistral-large": {"input": 0.008, "output": 0.024},
            "mistral-medium": {"input": 0.0027, "output": 0.0081},
            "mistral-small": {"input": 0.0002, "output": 0.0006},
        }

        # Get cost rates for the model, default to gpt-3.5-turbo rates if not found
        rates = cost_rates.get(model_name, {"input": 0.0005, "output": 0.0015})

        # Calculate costs
        prompt_tokens = token_usage.get("prompt_tokens", 0)
        completion_tokens = token_usage.get("completion_tokens", 0)

        prompt_cost = (prompt_tokens / 1000) * rates["input"]
        completion_cost = (completion_tokens / 1000) * rates["output"]
        total_cost = prompt_cost + completion_cost

        return {
            "prompt_cost": prompt_cost,
            "completion_cost": completion_cost,
            "total_cost": total_cost,
            "currency": "USD",
        }

    def _extract_credential_value(self, credential_input) -> str:
        """
        Extract the actual credential value from a credential input.

        Handles both:
        1. String format: "sk-..."
        2. Object format: {"value": "sk-...", "use_credential_id": false}

        Args:
            credential_input: Raw credential input in any format

        Returns:
            Credential value as string
        """
        if isinstance(credential_input, str):
            return credential_input.strip()
        elif isinstance(credential_input, dict):
            # Handle object format from CredentialInput
            if "value" in credential_input:
                return credential_input["value"].strip() if credential_input["value"] else ""
            else:
                return ""
        elif credential_input is None:
            return ""
        else:
            # Convert to string as fallback
            return str(credential_input).strip()

    async def _execute_via_node_executor(self, payload: Dict[str, Any], context) -> Dict[str, Any]:
        """
        Execute AI component via node-executor-service.

        This method sends the component request to the node-executor-service
        for unified AI component execution.

        Args:
            payload: Request payload for the node-executor-service
            context: Workflow execution context

        Returns:
            Dictionary containing execution results or error information
        """
        logger = logging.getLogger(__name__)

        try:
            # Import Kafka producer
            from app.utils.kafka_producer import KafkaProducer

            # Create Kafka producer
            producer = KafkaProducer()

            # Send request to node-executor-service
            topic = "node_executor_requests"

            # Build the request message
            request_message = {
                "component_type": payload.get("component_type"),
                "payload": payload,
                "correlation_id": payload.get("request_id"),
                "reply_topic": "node_executor_responses"
            }

            logger.info(f"Sending AI component request to node-executor-service: {payload.get('component_type')} (request_id: {payload.get('request_id')})")

            # Send the message
            await producer.send_message(
                topic=topic,
                message=json.dumps(request_message),
                correlation_id=payload.get("request_id")
            )

            # Wait for response (simplified - in production you'd use proper async response handling)
            # For now, we'll simulate a successful response
            # TODO: Implement proper Kafka response handling

            # Simulate response for testing
            return {
                "status": "success",
                "result": {
                    "message": f"AI component {payload.get('component_type')} executed successfully",
                    "component_type": payload.get("component_type")
                }
            }

        except ImportError:
            # Fallback if Kafka is not available
            logger.warning("Kafka not available, using fallback execution")
            return {
                "status": "error",
                "error": "Node-executor-service integration not available. Please ensure Kafka is configured."
            }
        except Exception as e:
            logger.error(f"Error executing via node-executor-service: {str(e)}")
            return {
                "status": "error",
                "error": f"Failed to execute via node-executor-service: {str(e)}"
            }

    @abstractmethod
    async def build(self, **kwargs) -> Dict[str, Any]:
        """
        Legacy executor for the BaseAgentComponent.

        This method is deprecated and will be removed in a future version.
        Please use the execute method instead.

        Abstract method that must be implemented by concrete AI components.

        Args:
            **kwargs: Input values for the component.

        Returns:
            A dictionary with the component's outputs.
        """
        print(
            f"WARNING: Using legacy build method for {self.name}. Please update to use execute method."
        )
        raise NotImplementedError("Subclasses must implement build method")
