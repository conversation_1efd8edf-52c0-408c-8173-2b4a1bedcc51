from typing import Any, Dict, Optional
from pydantic_settings import BaseSettings
from pydantic import PostgresDsn, validator

class Settings(BaseSettings):
    # Application settings
    ENV: str = "dev"
    APP_NAME: str = "workflow-builder-service"
    DEBUG: bool = False
    PORT: int = 50056
    GCS_CRED: str
    BUCKET_NAME: str
    # Database settingss
    DB_HOST: str
    DB_PORT: str
    DB_USER: str
    DB_PASSWORD: str
    DB_NAME: str
    SQLALCHEMY_DATABASE_URI: Optional[PostgresDsn] = None

    @validator("SQLALCHEMY_DATABASE_URI", pre=True)
    def assemble_db_connection(cls, v: Optional[str], values: Dict[str, Any]) -> Any:
        if isinstance(v, str):
            return v
        
        # Ensure all required values are present
        required = {"DB_USER", "DB_PASSWORD", "DB_HOST", "DB_PORT", "DB_NAME"}
        missing = required - values.keys()
        if missing:
            raise ValueError(f"Missing required database configuration: {missing}")

        return PostgresDsn.build(
            scheme="postgresql",
            username=values.get("DB_USER"),
            password=values.get("DB_PASSWORD"),
            host=values.get("DB_HOST"),
            port=int(values.get("DB_PORT", 5432)),
            path=values.get('DB_NAME')  # Note: no extra slash
        )

    # Redis settings
    REDIS_HOST: str
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0
    REDIS_PASSWORD: Optional[str] = None

    REPO_URL: str = ""
    GIT_TOKEN: str = ""

    FRONTEND_URL: str = ""
    BOOTSTRAP_SERVERS: str = ""

    # Conditional routing mode for workflow schema conversion
    CONDITIONAL_ROUTING_MODE: str = "embedded"

    # Model Provider API settings
    MODEL_PROVIDER_API_URL: str = "https://app-dev.rapidinnovation.dev/api/v1/models"
    MODEL_PROVIDER_API_TOKEN: Optional[str] = None

    class Config:
        env_file = ".env"
        case_sensitive = True

settings = Settings()
