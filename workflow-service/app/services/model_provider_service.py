"""
Service for fetching model providers and models from external API.
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
import httpx
import structlog

from app.core.config import settings

logger = structlog.get_logger(__name__)


class ModelProviderService:
    """Service to fetch model providers and models from external API."""
    
    def __init__(self):
        """Initialize the service with configuration."""
        self.api_url = settings.MODEL_PROVIDER_API_URL
        self.api_token = settings.MODEL_PROVIDER_API_TOKEN
        self.timeout = 30.0
        
        # Cache for providers and models
        self._providers_cache: Optional[List[str]] = None
        self._models_cache: Optional[List[str]] = None
        self._provider_models_cache: Optional[Dict[str, List[str]]] = None
        self._cache_timestamp: Optional[datetime] = None
        self._cache_duration = timedelta(minutes=15)  # Cache for 15 minutes
        
    def _is_cache_valid(self) -> bool:
        """Check if the cache is still valid."""
        if self._cache_timestamp is None:
            return False
        return datetime.now() - self._cache_timestamp < self._cache_duration
    
    def _get_headers(self) -> Dict[str, str]:
        """Get headers for API requests."""
        headers = {
            "accept": "application/json",
            "Content-Type": "application/json"
        }
        
        if self.api_token:
            headers["Authorization"] = f"Bearer {self.api_token}"
            
        return headers
    
    async def _fetch_models_from_api(self) -> Optional[List[Dict[str, Any]]]:
        """
        Fetch models from the external API.
        
        Returns:
            List of model dictionaries or None if failed
        """
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(
                    self.api_url,
                    headers=self._get_headers(),
                    params={"page": 1, "page_size": 100}
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get("success") and "models" in data:
                        logger.info(f"Successfully fetched {len(data['models'])} models from API")
                        return data["models"]
                    else:
                        logger.warning(f"API response indicates failure: {data.get('message', 'Unknown error')}")
                        return None
                else:
                    logger.error(f"API request failed with status {response.status_code}: {response.text}")
                    return None
                    
        except httpx.TimeoutException:
            logger.error("Timeout while fetching models from API")
            return None
        except httpx.RequestError as e:
            logger.error(f"Request error while fetching models: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error while fetching models: {str(e)}")
            return None
    
    def _process_models_data(self, models_data: List[Dict[str, Any]]) -> Tuple[List[str], List[str], Dict[str, List[str]]]:
        """
        Process the models data to extract providers, models, and provider-model mapping.
        
        Args:
            models_data: List of model dictionaries from API
            
        Returns:
            Tuple of (providers, models, provider_models_mapping)
        """
        providers = set()
        models = []
        provider_models = {}
        
        for model_data in models_data:
            # Skip inactive models
            if not model_data.get("isActive", True):
                continue
                
            model_name = model_data.get("model") or model_data.get("modelId")
            if not model_name:
                continue
                
            models.append(model_name)
            
            # Extract provider information
            provider_info = model_data.get("provider", {})
            provider_name = provider_info.get("provider")
            
            if provider_name:
                # Normalize provider name for consistency
                normalized_provider = self._normalize_provider_name(provider_name)
                providers.add(normalized_provider)
                
                if normalized_provider not in provider_models:
                    provider_models[normalized_provider] = []
                provider_models[normalized_provider].append(model_name)
        
        return sorted(list(providers)), models, provider_models
    
    def _normalize_provider_name(self, provider_name: str) -> str:
        """
        Normalize provider names to match existing conventions.
        
        Args:
            provider_name: Raw provider name from API
            
        Returns:
            Normalized provider name
        """
        # Convert to lowercase for comparison
        lower_name = provider_name.lower()
        
        # Map common provider names to standardized versions
        provider_mapping = {
            "openai": "OpenAI",
            "anthropic": "Anthropic", 
            "claude": "Claude",
            "google": "Google",
            "gemini": "Gemini",
            "mistral": "Mistral",
            "ollama": "Ollama",
            "azure": "Azure OpenAI",
            "alibaba": "Alibaba"
        }
        
        return provider_mapping.get(lower_name, provider_name.title())
    
    def _get_fallback_data(self) -> Tuple[List[str], List[str], Dict[str, List[str]]]:
        """
        Get fallback data when API is unavailable.
        
        Returns:
            Tuple of (providers, models, provider_models_mapping)
        """
        providers = [
            "OpenAI",
            "Azure OpenAI", 
            "Anthropic",
            "Claude",
            "Google",
            "Gemini",
            "Mistral",
            "Ollama",
            "Custom"
        ]
        
        models = [
            "gpt-4o", "gpt-4o-mini", "gpt-4-turbo", "gpt-4", "gpt-3.5-turbo",
            "claude-3-5-sonnet-20241022", "claude-3-5-haiku-20241022",
            "claude-3-opus-20240229", "claude-3-sonnet-20240229",
            "gemini-1.5-pro", "gemini-1.5-flash", "gemini-pro",
            "mistral-large-latest", "mistral-medium-latest",
            "llama3.2", "llama3.1", "llama3"
        ]
        
        provider_models = {
            "OpenAI": ["gpt-4o", "gpt-4o-mini", "gpt-4-turbo", "gpt-4", "gpt-3.5-turbo"],
            "Azure OpenAI": ["gpt-4", "gpt-4-turbo", "gpt-3.5-turbo"],
            "Anthropic": ["claude-3-5-sonnet-20241022", "claude-3-5-haiku-20241022", "claude-3-opus-20240229"],
            "Claude": ["claude-3-5-sonnet-20241022", "claude-3-5-haiku-20241022", "claude-3-opus-20240229"],
            "Google": ["gemini-1.5-pro", "gemini-1.5-flash", "gemini-pro"],
            "Gemini": ["gemini-1.5-pro", "gemini-1.5-flash", "gemini-pro"],
            "Mistral": ["mistral-large-latest", "mistral-medium-latest"],
            "Ollama": ["llama3.2", "llama3.1", "llama3"],
            "Custom": []
        }
        
        return providers, models, provider_models
    
    async def refresh_cache(self) -> bool:
        """
        Refresh the cache by fetching fresh data from API.
        
        Returns:
            True if cache was refreshed successfully, False otherwise
        """
        logger.info("Refreshing model provider cache...")
        
        models_data = await self._fetch_models_from_api()
        
        if models_data:
            providers, models, provider_models = self._process_models_data(models_data)
            
            # Update cache
            self._providers_cache = providers
            self._models_cache = models
            self._provider_models_cache = provider_models
            self._cache_timestamp = datetime.now()
            
            logger.info(f"Cache refreshed with {len(providers)} providers and {len(models)} models")
            return True
        else:
            logger.warning("Failed to refresh cache, using fallback data")
            providers, models, provider_models = self._get_fallback_data()
            
            # Update cache with fallback data
            self._providers_cache = providers
            self._models_cache = models
            self._provider_models_cache = provider_models
            self._cache_timestamp = datetime.now()
            
            return False
    
    async def get_providers(self) -> List[str]:
        """
        Get list of available model providers.
        
        Returns:
            List of provider names
        """
        if not self._is_cache_valid():
            await self.refresh_cache()
            
        return self._providers_cache or []
    
    async def get_models(self) -> List[str]:
        """
        Get list of available models.
        
        Returns:
            List of model names
        """
        if not self._is_cache_valid():
            await self.refresh_cache()
            
        return self._models_cache or []
    
    async def get_models_for_provider(self, provider: str) -> List[str]:
        """
        Get list of models for a specific provider.
        
        Args:
            provider: Provider name
            
        Returns:
            List of model names for the provider
        """
        if not self._is_cache_valid():
            await self.refresh_cache()
            
        if self._provider_models_cache:
            return self._provider_models_cache.get(provider, ["custom-model"])
        
        return ["custom-model"]


# Global instance
_model_provider_service: Optional[ModelProviderService] = None


def get_model_provider_service() -> ModelProviderService:
    """Get or create the global model provider service instance."""
    global _model_provider_service
    if _model_provider_service is None:
        _model_provider_service = ModelProviderService()
    return _model_provider_service
