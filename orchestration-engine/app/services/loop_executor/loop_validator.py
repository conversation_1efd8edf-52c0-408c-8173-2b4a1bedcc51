from typing import Dict, List, Any, Optional
from app.utils.enhanced_logger import get_logger


class LoopValidator:
    """
    Loop Validator for validating loop configurations and schemas.

    This class provides comprehensive validation for loop execution configurations,
    including schema validation, dependency checking, and configuration consistency.
    """

    def __init__(self):
        """Initialize the LoopValidator."""
        self.logger = get_logger("LoopValidator")

    def validate_loop_config(
        self,
        config: Dict[str, Any],
        transitions_by_id: Optional[Dict[str, Dict]] = None,
    ) -> Dict[str, Any]:
        """
        Validate a complete loop configuration.

        Args:
            config: Loop configuration dictionary to validate
            transitions_by_id: Optional dictionary of available transitions for validation

        Returns:
            Dictionary containing validation results with 'valid' boolean and 'errors' list
        """
        validation_result = {"valid": True, "errors": [], "warnings": []}

        try:
            # Basic structure validation
            structure_errors = self._validate_basic_structure(config)
            validation_result["errors"].extend(structure_errors)

            # Loop type validation
            loop_type_errors = self._validate_loop_type(config)
            validation_result["errors"].extend(loop_type_errors)

            # Iteration source validation
            iteration_source_errors = self._validate_iteration_source(config)
            validation_result["errors"].extend(iteration_source_errors)

            # Aggregation config validation
            aggregation_errors = self._validate_aggregation_config(config)
            validation_result["errors"].extend(aggregation_errors)

            # Loop body transitions validation
            if transitions_by_id:
                transition_errors = self._validate_loop_body_transitions(
                    config, transitions_by_id
                )
                validation_result["errors"].extend(transition_errors)

            # Concurrency configuration validation
            concurrency_errors = self._validate_concurrency_config(config)
            validation_result["errors"].extend(concurrency_errors)

            # Cross-field validation
            cross_field_errors = self._validate_cross_field_consistency(config)
            validation_result["errors"].extend(cross_field_errors)

            # Set overall validity
            validation_result["valid"] = len(validation_result["errors"]) == 0

            if validation_result["valid"]:
                self.logger.debug("Loop configuration validation passed")
            else:
                self.logger.warning(
                    f"Loop configuration validation failed with {len(validation_result['errors'])} errors"
                )

        except Exception as e:
            validation_result["valid"] = False
            validation_result["errors"].append(f"Validation exception: {str(e)}")
            self.logger.error(f"Loop configuration validation exception: {str(e)}")

        return validation_result

    def _validate_basic_structure(self, config: Dict[str, Any]) -> List[str]:
        """
        Validate basic structure of loop configuration.

        Args:
            config: Loop configuration dictionary

        Returns:
            List of validation error messages
        """
        errors = []

        if not isinstance(config, dict):
            errors.append("Loop configuration must be a dictionary")
            return errors

        # Required fields
        required_fields = [
            "loop_type",
            "iteration_source",
            "loop_body_transitions",
            "aggregation_config",
        ]

        for field in required_fields:
            if field not in config:
                errors.append(f"Required field missing: {field}")
            elif config[field] is None:
                errors.append(f"Required field cannot be null: {field}")

        return errors

    def _validate_loop_type(self, config: Dict[str, Any]) -> List[str]:
        """
        Validate loop type configuration.

        Args:
            config: Loop configuration dictionary

        Returns:
            List of validation error messages
        """
        errors = []

        loop_type = config.get("loop_type")
        if loop_type:
            valid_loop_types = ["context_preserving", "context_independent"]
            if loop_type not in valid_loop_types:
                errors.append(
                    f"Invalid loop_type: '{loop_type}'. Must be one of: {valid_loop_types}"
                )

        return errors

    def _validate_iteration_source(self, config: Dict[str, Any]) -> List[str]:
        """
        Validate iteration source configuration.

        Args:
            config: Loop configuration dictionary

        Returns:
            List of validation error messages
        """
        errors = []

        iteration_source = config.get("iteration_source")
        if not iteration_source:
            return errors

        if not isinstance(iteration_source, dict):
            errors.append("iteration_source must be a dictionary")
            return errors

        # Validate source type
        source_type = iteration_source.get("type")
        if not source_type:
            errors.append("iteration_source.type is required")
        else:
            valid_source_types = ["list", "range", "condition"]
            if source_type not in valid_source_types:
                errors.append(
                    f"Invalid iteration_source.type: '{source_type}'. Must be one of: {valid_source_types}"
                )

        # Validate source-specific data
        if source_type == "list":
            data = iteration_source.get("data")
            if data is None:
                errors.append("iteration_source.data is required for list type")
            elif not isinstance(data, list):
                errors.append("iteration_source.data must be an array for list type")
            elif len(data) == 0:
                errors.append("iteration_source.data cannot be empty for list type")

        elif source_type == "range":
            data = iteration_source.get("data")
            if not isinstance(data, dict):
                errors.append("iteration_source.data must be an object for range type")
            else:
                # Validate range parameters
                start = data.get("start", 0)
                stop = data.get("stop")
                step = data.get("step", 1)

                if stop is None:
                    errors.append(
                        "iteration_source.data.stop is required for range type"
                    )
                elif not isinstance(stop, int):
                    errors.append("iteration_source.data.stop must be an integer")

                if not isinstance(start, int):
                    errors.append("iteration_source.data.start must be an integer")

                if not isinstance(step, int):
                    errors.append("iteration_source.data.step must be an integer")
                elif step == 0:
                    errors.append("iteration_source.data.step cannot be zero")

                # Check for infinite loops
                if (
                    isinstance(start, int)
                    and isinstance(stop, int)
                    and isinstance(step, int)
                ):
                    if step > 0 and start >= stop:
                        errors.append("Invalid range: start >= stop with positive step")
                    elif step < 0 and start <= stop:
                        errors.append("Invalid range: start <= stop with negative step")

        elif source_type == "condition":
            condition = iteration_source.get("condition")
            if not condition:
                errors.append(
                    "iteration_source.condition is required for condition type"
                )
            # Additional condition validation could be added here

        return errors

    def _validate_aggregation_config(self, config: Dict[str, Any]) -> List[str]:
        """
        Validate aggregation configuration.

        Args:
            config: Loop configuration dictionary

        Returns:
            List of validation error messages
        """
        errors = []

        aggregation_config = config.get("aggregation_config")
        if not aggregation_config:
            return errors

        if not isinstance(aggregation_config, dict):
            errors.append("aggregation_config must be a dictionary")
            return errors

        # Validate aggregation type
        aggregation_type = aggregation_config.get("type")
        if not aggregation_type:
            errors.append("aggregation_config.type is required")
        else:
            valid_types = [
                "list",
                "object",
                "concatenate",
                "merge",
                "custom",
                "first",
                "last",
                "count",
            ]
            if aggregation_type not in valid_types:
                errors.append(
                    f"Invalid aggregation_config.type: '{aggregation_type}'. Must be one of: {valid_types}"
                )

        # Validate type-specific configurations
        if aggregation_type == "custom":
            function_name = aggregation_config.get("function_name")
            if not function_name:
                errors.append(
                    "aggregation_config.function_name is required for custom type"
                )

        elif aggregation_type == "concatenate":
            separator = aggregation_config.get("separator")
            if separator is not None and not isinstance(separator, str):
                errors.append("aggregation_config.separator must be a string")

        elif aggregation_type == "merge":
            merge_strategy = aggregation_config.get("merge_strategy", "overwrite")
            valid_strategies = ["overwrite", "append", "skip"]
            if merge_strategy not in valid_strategies:
                errors.append(
                    f"Invalid aggregation_config.merge_strategy: '{merge_strategy}'. Must be one of: {valid_strategies}"
                )

        return errors

    def _validate_loop_body_transitions(
        self, config: Dict[str, Any], transitions_by_id: Dict[str, Dict]
    ) -> List[str]:
        """
        Validate loop body transitions.

        Args:
            config: Loop configuration dictionary
            transitions_by_id: Dictionary of available transitions

        Returns:
            List of validation error messages
        """
        errors = []

        loop_body_transitions = config.get("loop_body_transitions")
        if loop_body_transitions is None:
            return errors

        if not isinstance(loop_body_transitions, list):
            errors.append("loop_body_transitions must be an array")
            return errors

        if len(loop_body_transitions) == 0:
            errors.append("loop_body_transitions cannot be empty")

        # Validate each transition ID
        for transition_id in loop_body_transitions:
            if not isinstance(transition_id, str):
                errors.append(
                    f"Loop body transition ID must be a string: {transition_id}"
                )
            elif transition_id not in transitions_by_id:
                errors.append(f"Loop body transition not found: {transition_id}")

        return errors

    def _validate_concurrency_config(self, config: Dict[str, Any]) -> List[str]:
        """
        Validate enhanced concurrency configuration.

        Args:
            config: Loop configuration dictionary

        Returns:
            List of validation error messages
        """
        errors = []

        concurrency = config.get("concurrency")
        if not concurrency:
            return errors

        if not isinstance(concurrency, dict):
            errors.append("concurrency must be a dictionary")
            return errors

        # Validate enabled flag
        enabled = concurrency.get("enabled")
        if enabled is not None and not isinstance(enabled, bool):
            errors.append("concurrency.enabled must be a boolean")

        # Validate max_concurrent
        max_concurrent = concurrency.get("max_concurrent")
        if max_concurrent is not None:
            if not isinstance(max_concurrent, int):
                errors.append("concurrency.max_concurrent must be an integer")
            elif max_concurrent <= 0:
                errors.append("concurrency.max_concurrent must be greater than 0")
            elif max_concurrent > 100:  # Reasonable upper limit
                errors.append(
                    "concurrency.max_concurrent should not exceed 100 for safety"
                )

        # Validate preserve_order flag
        preserve_order = concurrency.get("preserve_order")
        if preserve_order is not None and not isinstance(preserve_order, bool):
            errors.append("concurrency.preserve_order must be a boolean")

        # Validate early_exit flag
        early_exit = concurrency.get("early_exit")
        if early_exit is not None and not isinstance(early_exit, bool):
            errors.append("concurrency.early_exit must be a boolean")

        # Validate progress_tracking flag
        progress_tracking = concurrency.get("progress_tracking")
        if progress_tracking is not None and not isinstance(progress_tracking, bool):
            errors.append("concurrency.progress_tracking must be a boolean")

        # Validate iteration_timeout
        iteration_timeout = concurrency.get("iteration_timeout")
        if iteration_timeout is not None:
            if not isinstance(iteration_timeout, (int, float)):
                errors.append("concurrency.iteration_timeout must be a number")
            elif iteration_timeout <= 0:
                errors.append("concurrency.iteration_timeout must be greater than 0")

        # Validate early_exit_config
        early_exit_config = concurrency.get("early_exit_config")
        if early_exit_config is not None:
            if not isinstance(early_exit_config, dict):
                errors.append("concurrency.early_exit_config must be a dictionary")
            else:
                errors.extend(self._validate_early_exit_config(early_exit_config))

        # Validate resource_limits
        resource_limits = concurrency.get("resource_limits")
        if resource_limits is not None:
            if not isinstance(resource_limits, dict):
                errors.append("concurrency.resource_limits must be a dictionary")
            else:
                errors.extend(self._validate_resource_limits(resource_limits))

        return errors

    def _validate_early_exit_config(
        self, early_exit_config: Dict[str, Any]
    ) -> List[str]:
        """
        Validate early exit configuration.

        Args:
            early_exit_config: Early exit configuration dictionary

        Returns:
            List of validation error messages
        """
        errors = []

        # Validate on_first_success
        on_first_success = early_exit_config.get("on_first_success")
        if on_first_success is not None and not isinstance(on_first_success, bool):
            errors.append("early_exit_config.on_first_success must be a boolean")

        # Validate failure_threshold
        failure_threshold = early_exit_config.get("failure_threshold")
        if failure_threshold is not None:
            if not isinstance(failure_threshold, int):
                errors.append("early_exit_config.failure_threshold must be an integer")
            elif failure_threshold <= 0:
                errors.append(
                    "early_exit_config.failure_threshold must be greater than 0"
                )

        # Validate custom_condition
        custom_condition = early_exit_config.get("custom_condition")
        if custom_condition is not None:
            if not isinstance(custom_condition, str):
                errors.append("early_exit_config.custom_condition must be a string")
            elif len(custom_condition.strip()) == 0:
                errors.append("early_exit_config.custom_condition cannot be empty")

        return errors

    def _validate_resource_limits(self, resource_limits: Dict[str, Any]) -> List[str]:
        """
        Validate resource limits configuration.

        Args:
            resource_limits: Resource limits configuration dictionary

        Returns:
            List of validation error messages
        """
        errors = []

        # Validate memory_limit_mb
        memory_limit_mb = resource_limits.get("memory_limit_mb")
        if memory_limit_mb is not None:
            if not isinstance(memory_limit_mb, int):
                errors.append("resource_limits.memory_limit_mb must be an integer")
            elif memory_limit_mb <= 0:
                errors.append("resource_limits.memory_limit_mb must be greater than 0")

        # Validate cpu_limit_percent
        cpu_limit_percent = resource_limits.get("cpu_limit_percent")
        if cpu_limit_percent is not None:
            if not isinstance(cpu_limit_percent, (int, float)):
                errors.append("resource_limits.cpu_limit_percent must be a number")
            elif cpu_limit_percent <= 0 or cpu_limit_percent > 100:
                errors.append(
                    "resource_limits.cpu_limit_percent must be between 0 and 100"
                )

        # Validate max_execution_time
        max_execution_time = resource_limits.get("max_execution_time")
        if max_execution_time is not None:
            if not isinstance(max_execution_time, (int, float)):
                errors.append("resource_limits.max_execution_time must be a number")
            elif max_execution_time <= 0:
                errors.append(
                    "resource_limits.max_execution_time must be greater than 0"
                )

        return errors

    def _validate_cross_field_consistency(self, config: Dict[str, Any]) -> List[str]:
        """
        Validate consistency across different configuration fields.

        Args:
            config: Loop configuration dictionary

        Returns:
            List of validation error messages
        """
        errors = []

        # Check if context-preserving loops are compatible with concurrency
        loop_type = config.get("loop_type")
        concurrency = config.get("concurrency", {})

        if loop_type == "context_preserving" and concurrency.get("enabled", False):
            errors.append(
                "Context-preserving loops are not compatible with concurrent execution"
            )

        # Validate exit transition if present
        exit_transition = config.get("exit_transition")
        if exit_transition is not None and not isinstance(exit_transition, str):
            errors.append("exit_transition must be a string")

        return errors

    def validate_iteration_data(
        self, iteration_data: List[Any], max_iterations: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Validate iteration data before execution.

        Args:
            iteration_data: List of iteration data items
            max_iterations: Optional maximum number of iterations allowed

        Returns:
            Dictionary containing validation results
        """
        validation_result = {"valid": True, "errors": [], "warnings": []}

        if not isinstance(iteration_data, list):
            validation_result["errors"].append("Iteration data must be a list")
            validation_result["valid"] = False
            return validation_result

        if len(iteration_data) == 0:
            validation_result["warnings"].append("Iteration data is empty")

        if max_iterations and len(iteration_data) > max_iterations:
            validation_result["errors"].append(
                f"Too many iterations: {len(iteration_data)} > {max_iterations}"
            )
            validation_result["valid"] = False

        return validation_result

    def get_validation_schema(self) -> Dict[str, Any]:
        """
        Get the JSON schema for loop configuration validation.

        Returns:
            JSON schema dictionary
        """
        return {
            "type": "object",
            "required": [
                "loop_type",
                "iteration_source",
                "loop_body_transitions",
                "aggregation_config",
            ],
            "properties": {
                "loop_type": {
                    "type": "string",
                    "enum": ["context_preserving", "context_independent"],
                },
                "iteration_source": {
                    "type": "object",
                    "required": ["type"],
                    "properties": {
                        "type": {
                            "type": "string",
                            "enum": ["list", "range", "condition"],
                        },
                        "data": {"oneOf": [{"type": "array"}, {"type": "object"}]},
                    },
                },
                "loop_body_transitions": {
                    "type": "array",
                    "items": {"type": "string"},
                    "minItems": 1,
                },
                "aggregation_config": {
                    "type": "object",
                    "required": ["type"],
                    "properties": {
                        "type": {
                            "type": "string",
                            "enum": [
                                "list",
                                "object",
                                "concatenate",
                                "merge",
                                "custom",
                                "first",
                                "last",
                                "count",
                            ],
                        }
                    },
                },
                "concurrency": {
                    "type": "object",
                    "properties": {
                        "enabled": {"type": "boolean"},
                        "max_concurrent": {"type": "integer", "minimum": 1},
                    },
                },
                "exit_transition": {"type": "string"},
            },
        }
