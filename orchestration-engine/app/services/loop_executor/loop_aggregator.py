from typing import Dict, List, Any, Optional, Union, Callable
from app.utils.enhanced_logger import get_logger


class LoopAggregator:
    """
    Loop Aggregator for handling result aggregation strategies in loop execution.

    This class provides different aggregation strategies for collecting and combining
    results from loop iterations, including list aggregation, object aggregation,
    and custom aggregation functions.
    """

    def __init__(self):
        """Initialize the LoopAggregator."""
        self.logger = get_logger("LoopAggregator")
        self.custom_aggregators: Dict[str, Callable] = {}

    def register_custom_aggregator(self, name: str, aggregator_func: Callable) -> None:
        """
        Register a custom aggregation function.

        Args:
            name: Name of the custom aggregator
            aggregator_func: Function that takes a list of results and returns aggregated result
        """
        self.custom_aggregators[name] = aggregator_func
        self.logger.debug(f"Registered custom aggregator: {name}")

    def aggregate_results(
        self,
        results: Dict[int, Any],
        aggregation_config: Dict[str, Any],
        preserve_order: bool = True,
    ) -> Union[List[Any], Dict[str, Any], Any]:
        """
        Aggregate results based on the specified aggregation configuration.

        Args:
            results: Dictionary mapping iteration indices to results
            aggregation_config: Configuration specifying aggregation strategy
            preserve_order: Whether to preserve iteration order in results

        Returns:
            Aggregated results based on the strategy
        """
        if not results:
            self.logger.warning("No results to aggregate")
            return []

        aggregation_type = aggregation_config.get("type", "list")

        # Get ordered results if order preservation is required
        if preserve_order:
            ordered_results = self._get_ordered_results(results)
        else:
            ordered_results = list(results.values())

        self.logger.debug(
            f"Aggregating {len(ordered_results)} results using strategy: {aggregation_type}"
        )

        if aggregation_type == "list":
            return self._aggregate_as_list(ordered_results, aggregation_config)
        elif aggregation_type == "object":
            return self._aggregate_as_object(ordered_results, aggregation_config)
        elif aggregation_type == "concatenate":
            return self._aggregate_by_concatenation(ordered_results, aggregation_config)
        elif aggregation_type == "merge":
            return self._aggregate_by_merge(ordered_results, aggregation_config)
        elif aggregation_type == "custom":
            return self._aggregate_with_custom_function(
                ordered_results, aggregation_config
            )
        elif aggregation_type == "first":
            return self._aggregate_first_result(ordered_results)
        elif aggregation_type == "last":
            return self._aggregate_last_result(ordered_results)
        elif aggregation_type == "count":
            return self._aggregate_count(ordered_results)
        # New schema aggregation types
        elif aggregation_type == "collect_all":
            return self._aggregate_collect_all(ordered_results, aggregation_config)
        elif aggregation_type == "collect_successful":
            return self._aggregate_collect_successful(
                ordered_results, aggregation_config
            )
        elif aggregation_type == "count_only":
            return self._aggregate_count_only(ordered_results, aggregation_config)
        elif aggregation_type == "latest_only":
            return self._aggregate_latest_only(ordered_results, aggregation_config)
        elif aggregation_type == "first_success":
            return self._aggregate_first_success(ordered_results, aggregation_config)
        elif aggregation_type == "combine_text":
            return self._aggregate_combine_text(ordered_results, aggregation_config)
        # Phase 3: Advanced Aggregation Strategies
        elif aggregation_type == "statistical_summary":
            return self._aggregate_statistical_summary(ordered_results, aggregation_config)
        elif aggregation_type == "conditional_collect":
            return self._aggregate_conditional_collect(ordered_results, aggregation_config)
        elif aggregation_type == "hierarchical_merge":
            return self._aggregate_hierarchical_merge(ordered_results, aggregation_config)
        elif aggregation_type == "time_series":
            return self._aggregate_time_series(ordered_results, aggregation_config)
        elif aggregation_type == "weighted_average":
            return self._aggregate_weighted_average(ordered_results, aggregation_config)
        elif aggregation_type == "best_result":
            return self._aggregate_best_result(ordered_results, aggregation_config)
        elif aggregation_type == "consensus":
            return self._aggregate_consensus(ordered_results, aggregation_config)
        elif aggregation_type == "streaming_reduce":
            return self._aggregate_streaming_reduce(ordered_results, aggregation_config)
        else:
            self.logger.warning(
                f"Unknown aggregation type: {aggregation_type}, defaulting to list"
            )
            return self._aggregate_as_list(ordered_results, aggregation_config)

    def _get_ordered_results(self, results: Dict[int, Any]) -> List[Any]:
        """
        Get results ordered by iteration index.

        Args:
            results: Dictionary mapping iteration indices to results

        Returns:
            List of results in iteration order
        """
        ordered_results = []
        for index in sorted(results.keys()):
            ordered_results.append(results[index])
        return ordered_results

    def _aggregate_as_list(
        self, results: List[Any], config: Dict[str, Any]
    ) -> List[Any]:
        """
        Aggregate results as a simple list.

        Args:
            results: List of results to aggregate
            config: Aggregation configuration

        Returns:
            List of all results
        """
        # Optional filtering based on config
        filter_func = config.get("filter")
        if filter_func and callable(filter_func):
            results = [r for r in results if filter_func(r)]

        self.logger.debug(f"List aggregation completed with {len(results)} items")
        return results

    def _aggregate_as_object(
        self, results: List[Any], config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Aggregate results as an object with metadata.

        Args:
            results: List of results to aggregate
            config: Aggregation configuration

        Returns:
            Dictionary containing aggregated results and metadata
        """
        aggregated = {
            "results": results,
            "count": len(results),
            "aggregation_type": "object",
            "metadata": config.get("metadata", {}),
        }

        # Add success/failure counts if results have status
        success_count = 0
        failure_count = 0
        for result in results:
            if isinstance(result, dict):
                status = result.get("status", "unknown")
                if status == "completed" or status == "success":
                    success_count += 1
                elif status == "failed" or status == "error":
                    failure_count += 1

        if success_count > 0 or failure_count > 0:
            aggregated["success_count"] = success_count
            aggregated["failure_count"] = failure_count
            aggregated["success_rate"] = success_count / len(results) if results else 0

        self.logger.debug(f"Object aggregation completed with {len(results)} items")
        return aggregated

    def _aggregate_by_concatenation(
        self, results: List[Any], config: Dict[str, Any]
    ) -> str:
        """
        Aggregate results by concatenating string representations.

        Args:
            results: List of results to aggregate
            config: Aggregation configuration

        Returns:
            Concatenated string result
        """
        separator = config.get("separator", "")
        string_results = []

        for result in results:
            if isinstance(result, str):
                string_results.append(result)
            elif isinstance(result, dict) and "output" in result:
                string_results.append(str(result["output"]))
            else:
                string_results.append(str(result))

        concatenated = separator.join(string_results)
        self.logger.debug(
            f"Concatenation aggregation completed with {len(concatenated)} characters"
        )
        return concatenated

    def _aggregate_by_merge(
        self, results: List[Any], config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Aggregate results by merging dictionaries.

        Args:
            results: List of results to aggregate
            config: Aggregation configuration

        Returns:
            Merged dictionary result
        """
        merged = {}
        merge_strategy = config.get(
            "merge_strategy", "overwrite"
        )  # overwrite, append, skip

        for i, result in enumerate(results):
            if isinstance(result, dict):
                for key, value in result.items():
                    if key not in merged:
                        merged[key] = value
                    elif merge_strategy == "overwrite":
                        merged[key] = value
                    elif merge_strategy == "append":
                        if isinstance(merged[key], list):
                            if isinstance(value, list):
                                merged[key].extend(value)
                            else:
                                merged[key].append(value)
                        else:
                            merged[key] = [merged[key], value]
                    # skip strategy: keep existing value
            else:
                # Non-dict results get indexed keys
                merged[f"result_{i}"] = result

        self.logger.debug(f"Merge aggregation completed with {len(merged)} keys")
        return merged

    def _aggregate_with_custom_function(
        self, results: List[Any], config: Dict[str, Any]
    ) -> Any:
        """
        Aggregate results using a custom aggregation function.

        Args:
            results: List of results to aggregate
            config: Aggregation configuration

        Returns:
            Result from custom aggregation function
        """
        function_name = config.get("function_name")
        if not function_name:
            raise ValueError("Custom aggregation requires 'function_name' in config")

        if function_name not in self.custom_aggregators:
            raise ValueError(f"Custom aggregator '{function_name}' not registered")

        aggregator_func = self.custom_aggregators[function_name]

        try:
            result = aggregator_func(results)
            self.logger.debug(f"Custom aggregation '{function_name}' completed")
            return result
        except Exception as e:
            self.logger.error(f"Custom aggregation '{function_name}' failed: {str(e)}")
            raise

    def _aggregate_first_result(self, results: List[Any]) -> Any:
        """
        Return the first result.

        Args:
            results: List of results

        Returns:
            First result or None if empty
        """
        if results:
            self.logger.debug("First result aggregation completed")
            return results[0]
        return None

    def _aggregate_last_result(self, results: List[Any]) -> Any:
        """
        Return the last result.

        Args:
            results: List of results

        Returns:
            Last result or None if empty
        """
        if results:
            self.logger.debug("Last result aggregation completed")
            return results[-1]
        return None

    def _aggregate_count(self, results: List[Any]) -> int:
        """
        Return the count of results.

        Args:
            results: List of results

        Returns:
            Number of results
        """
        count = len(results)
        self.logger.debug(f"Count aggregation completed: {count}")
        return count

    # New schema aggregation methods

    def _aggregate_collect_all(
        self, results: List[Any], config: Dict[str, Any]
    ) -> List[Any]:
        """
        Collect all results (equivalent to list aggregation with optional metadata).

        Args:
            results: List of results to aggregate
            config: Aggregation configuration

        Returns:
            List of all results with optional metadata
        """
        include_metadata = config.get("include_metadata", False)

        if include_metadata:
            return {
                "results": results,
                "metadata": {
                    "total_count": len(results),
                    "aggregation_type": "collect_all",
                    "timestamp": self._get_current_timestamp(),
                },
            }

        self.logger.debug(
            f"Collect all aggregation completed with {len(results)} items"
        )
        return results

    def _aggregate_collect_successful(
        self, results: List[Any], config: Dict[str, Any]
    ) -> List[Any]:
        """
        Collect only successful results.

        Args:
            results: List of results to aggregate
            config: Aggregation configuration

        Returns:
            List of successful results
        """
        successful_results = []

        for result in results:
            if self._is_result_successful(result):
                successful_results.append(result)

        include_metadata = config.get("include_metadata", False)

        if include_metadata:
            return {
                "results": successful_results,
                "metadata": {
                    "successful_count": len(successful_results),
                    "total_count": len(results),
                    "success_rate": (
                        len(successful_results) / len(results) if results else 0
                    ),
                    "aggregation_type": "collect_successful",
                    "timestamp": self._get_current_timestamp(),
                },
            }

        self.logger.debug(
            f"Collect successful aggregation completed with {len(successful_results)}/{len(results)} items"
        )
        return successful_results

    def _aggregate_count_only(
        self, results: List[Any], config: Dict[str, Any]
    ) -> Dict[str, int]:
        """
        Return only count information.

        Args:
            results: List of results to aggregate
            config: Aggregation configuration

        Returns:
            Dictionary with count information
        """
        successful_count = sum(
            1 for result in results if self._is_result_successful(result)
        )
        failed_count = len(results) - successful_count

        count_info = {
            "total_count": len(results),
            "successful_count": successful_count,
            "failed_count": failed_count,
        }

        include_metadata = config.get("include_metadata", False)
        if include_metadata:
            count_info["metadata"] = {
                "aggregation_type": "count_only",
                "timestamp": self._get_current_timestamp(),
            }

        self.logger.debug(f"Count only aggregation completed: {count_info}")
        return count_info

    def _aggregate_latest_only(self, results: List[Any], config: Dict[str, Any]) -> Any:
        """
        Return only the latest (last) result.

        Args:
            results: List of results to aggregate
            config: Aggregation configuration

        Returns:
            Latest result or None if empty
        """
        if not results:
            return None

        latest_result = results[-1]

        include_metadata = config.get("include_metadata", False)
        if include_metadata:
            return {
                "result": latest_result,
                "metadata": {
                    "total_count": len(results),
                    "aggregation_type": "latest_only",
                    "timestamp": self._get_current_timestamp(),
                },
            }

        self.logger.debug("Latest only aggregation completed")
        return latest_result

    def _aggregate_first_success(
        self, results: List[Any], config: Dict[str, Any]
    ) -> Any:
        """
        Return the first successful result.

        Args:
            results: List of results to aggregate
            config: Aggregation configuration

        Returns:
            First successful result or None if no success
        """
        for result in results:
            if self._is_result_successful(result):
                include_metadata = config.get("include_metadata", False)
                if include_metadata:
                    return {
                        "result": result,
                        "metadata": {
                            "total_count": len(results),
                            "aggregation_type": "first_success",
                            "timestamp": self._get_current_timestamp(),
                        },
                    }

                self.logger.debug("First success aggregation completed")
                return result

        self.logger.debug(
            "First success aggregation completed - no successful results found"
        )
        return None

    def _aggregate_combine_text(
        self, results: List[Any], config: Dict[str, Any]
    ) -> str:
        """
        Combine results as text with configurable separator.

        Args:
            results: List of results to aggregate
            config: Aggregation configuration

        Returns:
            Combined text string
        """
        separator = config.get("separator", "\n")
        text_parts = []

        for result in results:
            if isinstance(result, str):
                text_parts.append(result)
            elif isinstance(result, dict):
                # Try to extract text from common fields
                text_value = (
                    result.get("text")
                    or result.get("output")
                    or result.get("result")
                    or str(result)
                )
                text_parts.append(str(text_value))
            else:
                text_parts.append(str(result))

        combined_text = separator.join(text_parts)

        include_metadata = config.get("include_metadata", False)
        if include_metadata:
            return {
                "text": combined_text,
                "metadata": {
                    "total_count": len(results),
                    "character_count": len(combined_text),
                    "separator": separator,
                    "aggregation_type": "combine_text",
                    "timestamp": self._get_current_timestamp(),
                },
            }

        self.logger.debug(
            f"Combine text aggregation completed with {len(combined_text)} characters"
        )
        return combined_text

    def _is_result_successful(self, result: Any) -> bool:
        """
        Determine if a result represents success.

        Args:
            result: Result to check

        Returns:
            True if result is successful
        """
        if isinstance(result, dict):
            status = result.get("status")
            if status:
                return status in ["success", "completed", "ok"]

            # Check for error field (absence indicates success)
            if "error" not in result:
                return True

            # Check for result data (presence indicates success)
            if "result" in result or "data" in result:
                return True

        # For non-dict results, assume success if not None
        return result is not None

    def _get_current_timestamp(self) -> float:
        """
        Get current timestamp.

        Returns:
            Current timestamp as float
        """
        import time

        return time.time()

    # Phase 3: Advanced Aggregation Strategies

    def _aggregate_statistical_summary(
        self, results: List[Any], config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Generate statistical summary of numerical results.

        Args:
            results: List of results to aggregate
            config: Aggregation configuration

        Returns:
            Statistical summary dictionary
        """
        numerical_values = []

        for result in results:
            if isinstance(result, (int, float)):
                numerical_values.append(result)
            elif isinstance(result, dict):
                # Try to extract numerical value from common fields
                value = result.get("value") or result.get("score") or result.get("result")
                if isinstance(value, (int, float)):
                    numerical_values.append(value)

        if not numerical_values:
            return {
                "error": "No numerical values found for statistical summary",
                "total_results": len(results)
            }

        import statistics

        summary = {
            "count": len(numerical_values),
            "sum": sum(numerical_values),
            "mean": statistics.mean(numerical_values),
            "median": statistics.median(numerical_values),
            "min": min(numerical_values),
            "max": max(numerical_values),
            "range": max(numerical_values) - min(numerical_values)
        }

        if len(numerical_values) > 1:
            summary["std_dev"] = statistics.stdev(numerical_values)
            summary["variance"] = statistics.variance(numerical_values)

        include_metadata = config.get("include_metadata", False)
        if include_metadata:
            summary["metadata"] = {
                "aggregation_type": "statistical_summary",
                "timestamp": self._get_current_timestamp(),
                "total_results": len(results),
                "numerical_results": len(numerical_values)
            }

        self.logger.debug(
            f"Statistical summary aggregation completed: {len(numerical_values)} values"
        )
        return summary

    def _aggregate_conditional_collect(
        self, results: List[Any], config: Dict[str, Any]
    ) -> List[Any]:
        """
        Collect results based on custom conditions.

        Args:
            results: List of results to aggregate
            config: Aggregation configuration with conditions

        Returns:
            List of results matching conditions
        """
        conditions = config.get("conditions", {})
        collected_results = []

        for result in results:
            if self._evaluate_conditions(result, conditions):
                collected_results.append(result)

        include_metadata = config.get("include_metadata", False)
        if include_metadata:
            return {
                "results": collected_results,
                "metadata": {
                    "matched_count": len(collected_results),
                    "total_count": len(results),
                    "match_rate": len(collected_results) / len(results) if results else 0,
                    "conditions": conditions,
                    "aggregation_type": "conditional_collect",
                    "timestamp": self._get_current_timestamp()
                }
            }

        self.logger.debug(
            f"Conditional collect aggregation: {len(collected_results)}/{len(results)} matched"
        )
        return collected_results

    def _evaluate_conditions(self, result: Any, conditions: Dict[str, Any]) -> bool:
        """Evaluate if result meets specified conditions."""

        for field, condition in conditions.items():
            if isinstance(result, dict):
                value = result.get(field)
            else:
                # For non-dict results, only support basic conditions
                if field != "_value":
                    continue
                value = result

            if not self._check_condition(value, condition):
                return False

        return True

    def _check_condition(self, value: Any, condition: Dict[str, Any]) -> bool:
        """Check if value meets a specific condition."""

        operator = condition.get("operator", "equals")
        expected = condition.get("value")

        if operator == "equals":
            return value == expected
        elif operator == "not_equals":
            return value != expected
        elif operator == "greater_than":
            return isinstance(value, (int, float)) and value > expected
        elif operator == "less_than":
            return isinstance(value, (int, float)) and value < expected
        elif operator == "contains":
            return isinstance(value, str) and expected in value
        elif operator == "in":
            return value in expected if isinstance(expected, (list, tuple)) else False
        else:
            return False

    def _aggregate_hierarchical_merge(
        self, results: List[Any], config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Merge results hierarchically based on specified structure.

        Args:
            results: List of results to aggregate
            config: Aggregation configuration with hierarchy definition

        Returns:
            Hierarchically merged result
        """
        hierarchy = config.get("hierarchy", ["category", "subcategory", "item"])
        merged = {}

        for result in results:
            if not isinstance(result, dict):
                continue

            current_level = merged
            for level in hierarchy[:-1]:
                key = result.get(level, "unknown")
                if key not in current_level:
                    current_level[key] = {}
                current_level = current_level[key]

            # Add the final level
            final_key = result.get(hierarchy[-1], "unknown")
            if final_key not in current_level:
                current_level[final_key] = []
            current_level[final_key].append(result)

        include_metadata = config.get("include_metadata", False)
        if include_metadata:
            return {
                "hierarchy": merged,
                "metadata": {
                    "total_results": len(results),
                    "hierarchy_levels": hierarchy,
                    "aggregation_type": "hierarchical_merge",
                    "timestamp": self._get_current_timestamp()
                }
            }

        self.logger.debug(f"Hierarchical merge completed with {len(hierarchy)} levels")
        return merged

    def _aggregate_time_series(
        self, results: List[Any], config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Aggregate results as time series data.

        Args:
            results: List of results to aggregate
            config: Aggregation configuration with time field

        Returns:
            Time series aggregated data
        """
        time_field = config.get("time_field", "timestamp")
        value_field = config.get("value_field", "value")
        interval = config.get("interval", "auto")  # auto, minute, hour, day

        time_series = []

        for result in results:
            if isinstance(result, dict):
                timestamp = result.get(time_field)
                value = result.get(value_field)

                if timestamp is not None and value is not None:
                    time_series.append({
                        "timestamp": timestamp,
                        "value": value,
                        "original_result": result
                    })

        # Sort by timestamp
        time_series.sort(key=lambda x: x["timestamp"])

        # Group by interval if specified
        if interval != "auto":
            time_series = self._group_by_time_interval(time_series, interval)

        include_metadata = config.get("include_metadata", False)
        if include_metadata:
            return {
                "time_series": time_series,
                "metadata": {
                    "total_points": len(time_series),
                    "time_field": time_field,
                    "value_field": value_field,
                    "interval": interval,
                    "aggregation_type": "time_series",
                    "timestamp": self._get_current_timestamp()
                }
            }

        self.logger.debug(f"Time series aggregation completed with {len(time_series)} points")
        return time_series

    def _group_by_time_interval(self, time_series: List[Dict], interval: str) -> List[Dict]:
        """Group time series data by specified interval."""

        if not time_series:
            return []

        # Simple grouping implementation - can be enhanced
        grouped = {}

        for point in time_series:
            # Create interval key based on timestamp
            timestamp = point["timestamp"]
            if interval == "minute":
                key = int(timestamp // 60) * 60
            elif interval == "hour":
                key = int(timestamp // 3600) * 3600
            elif interval == "day":
                key = int(timestamp // 86400) * 86400
            else:
                key = timestamp

            if key not in grouped:
                grouped[key] = []
            grouped[key].append(point)

        # Convert to list and aggregate values within each interval
        result = []
        for interval_start, points in sorted(grouped.items()):
            values = [p["value"] for p in points if isinstance(p["value"], (int, float))]
            if values:
                result.append({
                    "interval_start": interval_start,
                    "count": len(points),
                    "sum": sum(values),
                    "avg": sum(values) / len(values),
                    "min": min(values),
                    "max": max(values),
                    "points": points
                })

        return result

    def _aggregate_weighted_average(
        self, results: List[Any], config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Calculate weighted average of results.

        Args:
            results: List of results to aggregate
            config: Aggregation configuration with weight field

        Returns:
            Weighted average result
        """
        value_field = config.get("value_field", "value")
        weight_field = config.get("weight_field", "weight")

        weighted_sum = 0.0
        total_weight = 0.0
        valid_results = 0

        for result in results:
            if isinstance(result, dict):
                value = result.get(value_field)
                weight = result.get(weight_field, 1.0)  # Default weight is 1.0

                if isinstance(value, (int, float)) and isinstance(weight, (int, float)):
                    weighted_sum += value * weight
                    total_weight += weight
                    valid_results += 1

        if total_weight == 0:
            return {
                "error": "No valid weighted values found",
                "total_results": len(results)
            }

        weighted_average = weighted_sum / total_weight

        result = {
            "weighted_average": weighted_average,
            "total_weight": total_weight,
            "valid_results": valid_results,
            "total_results": len(results)
        }

        include_metadata = config.get("include_metadata", False)
        if include_metadata:
            result["metadata"] = {
                "value_field": value_field,
                "weight_field": weight_field,
                "aggregation_type": "weighted_average",
                "timestamp": self._get_current_timestamp()
            }

        self.logger.debug(f"Weighted average aggregation: {weighted_average:.4f}")
        return result

    def _aggregate_best_result(
        self, results: List[Any], config: Dict[str, Any]
    ) -> Any:
        """
        Select the best result based on specified criteria.

        Args:
            results: List of results to aggregate
            config: Aggregation configuration with selection criteria

        Returns:
            Best result based on criteria
        """
        criteria = config.get("criteria", {})
        score_field = criteria.get("score_field", "score")
        maximize = criteria.get("maximize", True)  # True for max, False for min

        best_result = None
        best_score = float('-inf') if maximize else float('inf')

        for result in results:
            if isinstance(result, dict):
                score = result.get(score_field)
                if isinstance(score, (int, float)):
                    if (maximize and score > best_score) or (not maximize and score < best_score):
                        best_score = score
                        best_result = result

        if best_result is None:
            return {
                "error": "No valid results found for best selection",
                "total_results": len(results)
            }

        include_metadata = config.get("include_metadata", False)
        if include_metadata:
            return {
                "best_result": best_result,
                "metadata": {
                    "best_score": best_score,
                    "score_field": score_field,
                    "maximize": maximize,
                    "total_results": len(results),
                    "aggregation_type": "best_result",
                    "timestamp": self._get_current_timestamp()
                }
            }

        self.logger.debug(f"Best result selected with score: {best_score}")
        return best_result

    def _aggregate_consensus(
        self, results: List[Any], config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Find consensus among results based on frequency or agreement.

        Args:
            results: List of results to aggregate
            config: Aggregation configuration with consensus criteria

        Returns:
            Consensus result
        """
        consensus_field = config.get("consensus_field", "result")
        threshold = config.get("threshold", 0.5)  # Minimum agreement ratio

        value_counts = {}
        total_valid = 0

        for result in results:
            if isinstance(result, dict):
                value = result.get(consensus_field)
            else:
                value = result

            if value is not None:
                # Convert to string for consistent comparison
                value_str = str(value)
                value_counts[value_str] = value_counts.get(value_str, 0) + 1
                total_valid += 1

        if not value_counts:
            return {
                "error": "No valid values found for consensus",
                "total_results": len(results)
            }

        # Find the most frequent value
        most_frequent = max(value_counts.items(), key=lambda x: x[1])
        consensus_value, count = most_frequent
        agreement_ratio = count / total_valid

        has_consensus = agreement_ratio >= threshold

        result = {
            "consensus_value": consensus_value,
            "agreement_count": count,
            "total_valid": total_valid,
            "agreement_ratio": agreement_ratio,
            "has_consensus": has_consensus,
            "threshold": threshold,
            "value_distribution": dict(value_counts)
        }

        include_metadata = config.get("include_metadata", False)
        if include_metadata:
            result["metadata"] = {
                "consensus_field": consensus_field,
                "aggregation_type": "consensus",
                "timestamp": self._get_current_timestamp()
            }

        self.logger.debug(
            f"Consensus aggregation: {agreement_ratio:.2f} agreement on '{consensus_value}'"
        )
        return result

    def _aggregate_streaming_reduce(
        self, results: List[Any], config: Dict[str, Any]
    ) -> Any:
        """
        Apply streaming reduce operation on results.

        Args:
            results: List of results to aggregate
            config: Aggregation configuration with reduce function

        Returns:
            Reduced result
        """
        reduce_function = config.get("reduce_function", "sum")
        initial_value = config.get("initial_value", 0)
        value_field = config.get("value_field", "value")

        accumulator = initial_value
        processed_count = 0

        for result in results:
            if isinstance(result, dict):
                value = result.get(value_field)
            else:
                value = result

            if value is not None:
                try:
                    if reduce_function == "sum":
                        if isinstance(value, (int, float)):
                            accumulator += value
                            processed_count += 1
                    elif reduce_function == "product":
                        if isinstance(value, (int, float)):
                            accumulator *= value
                            processed_count += 1
                    elif reduce_function == "concat":
                        accumulator = str(accumulator) + str(value)
                        processed_count += 1
                    elif reduce_function == "max":
                        if isinstance(value, (int, float)):
                            accumulator = max(accumulator, value)
                            processed_count += 1
                    elif reduce_function == "min":
                        if isinstance(value, (int, float)):
                            accumulator = min(accumulator, value)
                            processed_count += 1
                    elif reduce_function == "custom":
                        # For custom reduce functions - placeholder for future enhancement
                        pass
                except Exception as e:
                    self.logger.warning(f"Error in streaming reduce: {str(e)}")
                    continue

        result_data = {
            "result": accumulator,
            "processed_count": processed_count,
            "total_results": len(results),
            "reduce_function": reduce_function
        }

        include_metadata = config.get("include_metadata", False)
        if include_metadata:
            result_data["metadata"] = {
                "initial_value": initial_value,
                "value_field": value_field,
                "aggregation_type": "streaming_reduce",
                "timestamp": self._get_current_timestamp()
            }

        self.logger.debug(
            f"Streaming reduce ({reduce_function}): processed {processed_count}/{len(results)} results"
        )
        return result_data

    def validate_aggregation_config(self, config: Dict[str, Any]) -> bool:
        """
        Validate aggregation configuration.

        Args:
            config: Aggregation configuration to validate

        Returns:
            True if valid, False otherwise
        """
        if not isinstance(config, dict):
            self.logger.error("Aggregation config must be a dictionary")
            return False

        aggregation_type = config.get("type")
        if not aggregation_type:
            self.logger.error("Aggregation config must specify 'type'")
            return False

        valid_types = [
            "list",
            "object",
            "concatenate",
            "merge",
            "custom",
            "first",
            "last",
            "count",
            # New schema aggregation types
            "collect_all",
            "collect_successful",
            "count_only",
            "latest_only",
            "first_success",
            "combine_text",
        ]
        if aggregation_type not in valid_types:
            self.logger.error(f"Invalid aggregation type: {aggregation_type}")
            return False

        # Validate custom aggregation config
        if aggregation_type == "custom":
            function_name = config.get("function_name")
            if not function_name:
                self.logger.error("Custom aggregation requires 'function_name'")
                return False
            if function_name not in self.custom_aggregators:
                self.logger.error(f"Custom aggregator '{function_name}' not registered")
                return False

        self.logger.debug(
            f"Aggregation config validation passed for type: {aggregation_type}"
        )
        return True

    def get_aggregation_summary(self, results: Dict[int, Any]) -> Dict[str, Any]:
        """
        Get a summary of results before aggregation.

        Args:
            results: Dictionary mapping iteration indices to results

        Returns:
            Summary dictionary with statistics
        """
        if not results:
            return {"total_results": 0, "indices": [], "result_types": {}}

        indices = sorted(results.keys())
        result_types = {}

        for result in results.values():
            result_type = type(result).__name__
            result_types[result_type] = result_types.get(result_type, 0) + 1

        summary = {
            "total_results": len(results),
            "indices": indices,
            "index_range": f"{min(indices)}-{max(indices)}" if indices else "empty",
            "result_types": result_types,
            "has_gaps": (
                len(indices) != (max(indices) - min(indices) + 1) if indices else False
            ),
        }

        return summary

    def _aggregate_custom(
        self, results: List[Any], config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Apply custom aggregation function.

        Args:
            results: List of iteration results
            config: Aggregation configuration

        Returns:
            Dict containing aggregated result and metadata
        """
        custom_function = config.get("custom_function")
        if not custom_function:
            raise ValueError("Custom aggregation requires 'custom_function' in config")

        try:
            # Validate and execute custom function
            aggregated_result = self._execute_custom_function(
                custom_function, results, config
            )

            metadata = {
                "total_results": len(results),
                "custom_function": custom_function.get("name", "anonymous"),
                "function_type": custom_function.get("type", "unknown"),
                "execution_time": 0,  # Will be measured during execution
            }

            return {
                "aggregated_result": aggregated_result,
                "metadata": metadata,
            }

        except Exception as e:
            self.logger.error(f"❌ Custom aggregation failed: {str(e)}")
            raise

    def _execute_custom_function(
        self,
        custom_function: Dict[str, Any],
        results: List[Any],
        config: Dict[str, Any],
    ) -> Any:
        """
        Execute a custom aggregation function safely.

        Args:
            custom_function: Custom function configuration
            results: List of iteration results
            config: Full aggregation configuration

        Returns:
            Result of custom function execution
        """
        import time

        start_time = time.time()

        function_type = custom_function.get("type", "code")

        if function_type == "code":
            return self._execute_code_function(custom_function, results, config)
        elif function_type == "builtin":
            return self._execute_builtin_function(custom_function, results, config)
        elif function_type == "lambda":
            return self._execute_lambda_function(custom_function, results, config)
        else:
            raise ValueError(f"Unsupported custom function type: {function_type}")

    def _execute_code_function(
        self,
        custom_function: Dict[str, Any],
        results: List[Any],
        config: Dict[str, Any],
    ) -> Any:
        """
        Execute custom code function with safety checks.

        Args:
            custom_function: Function configuration with code
            results: List of iteration results
            config: Aggregation configuration

        Returns:
            Result of code execution
        """
        code = custom_function.get("code")
        if not code:
            raise ValueError("Code function requires 'code' field")

        # Safety validation
        self._validate_custom_code(code)

        # Create safe execution context
        safe_context = self._create_safe_context(results, config)

        try:
            # Execute code in restricted environment
            exec(code, safe_context)

            # Get result from context
            if "result" not in safe_context:
                raise ValueError("Custom function must set 'result' variable")

            return safe_context["result"]

        except Exception as e:
            self.logger.error(f"❌ Custom code execution failed: {str(e)}")
            raise

    def _execute_builtin_function(
        self,
        custom_function: Dict[str, Any],
        results: List[Any],
        config: Dict[str, Any],
    ) -> Any:
        """
        Execute predefined builtin aggregation function.

        Args:
            custom_function: Function configuration
            results: List of iteration results
            config: Aggregation configuration

        Returns:
            Result of builtin function
        """
        function_name = custom_function.get("name")
        if not function_name:
            raise ValueError("Builtin function requires 'name' field")

        # Define available builtin functions
        builtin_functions = {
            "average": lambda r: (
                sum(self._extract_numeric_values(r)) / len(r) if r else 0
            ),
            "median": lambda r: self._calculate_median(self._extract_numeric_values(r)),
            "mode": lambda r: self._calculate_mode(r),
            "variance": lambda r: self._calculate_variance(
                self._extract_numeric_values(r)
            ),
            "std_dev": lambda r: self._calculate_std_dev(
                self._extract_numeric_values(r)
            ),
            "unique": lambda r: list(set(r)),
            "flatten": lambda r: self._flatten_results(r),
            "group_by": lambda r: self._group_results(r, custom_function.get("key")),
            "filter": lambda r: self._filter_results(
                r, custom_function.get("condition")
            ),
            "transform": lambda r: self._transform_results(
                r, custom_function.get("transformation")
            ),
        }

        if function_name not in builtin_functions:
            raise ValueError(f"Unknown builtin function: {function_name}")

        return builtin_functions[function_name](results)

    def _execute_lambda_function(
        self,
        custom_function: Dict[str, Any],
        results: List[Any],
        config: Dict[str, Any],
    ) -> Any:
        """
        Execute lambda-style function.

        Args:
            custom_function: Function configuration
            results: List of iteration results
            config: Aggregation configuration

        Returns:
            Result of lambda execution
        """
        expression = custom_function.get("expression")
        if not expression:
            raise ValueError("Lambda function requires 'expression' field")

        # Safety validation
        self._validate_lambda_expression(expression)

        # Create safe context
        safe_context = self._create_safe_context(results, config)

        try:
            # Evaluate lambda expression
            return eval(expression, {"__builtins__": {}}, safe_context)
        except Exception as e:
            self.logger.error(f"❌ Lambda execution failed: {str(e)}")
            raise

    def _validate_custom_code(self, code: str) -> None:
        """
        Validate custom code for security and safety.

        Args:
            code: Code string to validate

        Raises:
            ValueError: If code contains dangerous patterns
        """
        dangerous_patterns = [
            "import os",
            "import sys",
            "import subprocess",
            "import socket",
            "open(",
            "file(",
            "exec(",
            "eval(",
            "compile(",
            "__import__",
            "globals()",
            "locals()",
            "vars()",
            "dir()",
            "getattr",
            "setattr",
            "delattr",
            "hasattr",
            "input(",
            "raw_input(",
            "reload(",
        ]

        code_lower = code.lower()
        for pattern in dangerous_patterns:
            if pattern in code_lower:
                raise ValueError(
                    f"Dangerous pattern detected in custom code: {pattern}"
                )

        # Check for excessive complexity
        if len(code) > 10000:  # 10KB limit
            raise ValueError("Custom code too large (max 10KB)")

        if code.count("\n") > 100:  # 100 lines limit
            raise ValueError("Custom code too complex (max 100 lines)")

    def _validate_lambda_expression(self, expression: str) -> None:
        """
        Validate lambda expression for safety.

        Args:
            expression: Lambda expression to validate

        Raises:
            ValueError: If expression contains dangerous patterns
        """
        dangerous_patterns = [
            "import",
            "exec",
            "eval",
            "__",
            "open",
            "file",
            "input",
            "compile",
            "globals",
            "locals",
            "vars",
            "dir",
        ]

        expr_lower = expression.lower()
        for pattern in dangerous_patterns:
            if pattern in expr_lower:
                raise ValueError(f"Dangerous pattern in lambda expression: {pattern}")

        if len(expression) > 1000:  # 1KB limit for expressions
            raise ValueError("Lambda expression too long (max 1KB)")

    def _create_safe_context(
        self, results: List[Any], config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Create safe execution context for custom functions.

        Args:
            results: List of iteration results
            config: Aggregation configuration

        Returns:
            Safe context dictionary
        """
        # Safe builtin functions
        safe_builtins = {
            "len": len,
            "str": str,
            "int": int,
            "float": float,
            "bool": bool,
            "min": min,
            "max": max,
            "sum": sum,
            "abs": abs,
            "round": round,
            "any": any,
            "all": all,
            "sorted": sorted,
            "reversed": reversed,
            "enumerate": enumerate,
            "zip": zip,
            "range": range,
            "list": list,
            "dict": dict,
            "set": set,
            "tuple": tuple,
            "type": type,
        }

        # Safe math functions
        import math

        safe_math = {
            "sqrt": math.sqrt,
            "pow": math.pow,
            "log": math.log,
            "log10": math.log10,
            "sin": math.sin,
            "cos": math.cos,
            "tan": math.tan,
            "pi": math.pi,
            "e": math.e,
            "ceil": math.ceil,
            "floor": math.floor,
            "fabs": math.fabs,
        }

        return {
            "__builtins__": safe_builtins,
            "math": safe_math,
            "results": results,
            "config": config,
            "len": len,
            "sum": sum,
            "min": min,
            "max": max,
            "sorted": sorted,
            "enumerate": enumerate,
        }

    # Helper methods for builtin functions
    def _extract_numeric_values(self, results: List[Any]) -> List[float]:
        """Extract numeric values from results."""
        numeric_values = []
        for result in results:
            if isinstance(result, (int, float)):
                numeric_values.append(float(result))
            elif isinstance(result, dict) and "value" in result:
                try:
                    numeric_values.append(float(result["value"]))
                except (ValueError, TypeError):
                    continue
        return numeric_values

    def _calculate_median(self, values: List[float]) -> float:
        """Calculate median of numeric values."""
        if not values:
            return 0.0
        sorted_values = sorted(values)
        n = len(sorted_values)
        if n % 2 == 0:
            return (sorted_values[n // 2 - 1] + sorted_values[n // 2]) / 2
        return sorted_values[n // 2]

    def _calculate_mode(self, results: List[Any]) -> Any:
        """Calculate mode (most frequent value)."""
        if not results:
            return None
        from collections import Counter

        counter = Counter(results)
        return counter.most_common(1)[0][0]

    def _calculate_variance(self, values: List[float]) -> float:
        """Calculate variance of numeric values."""
        if len(values) < 2:
            return 0.0
        mean = sum(values) / len(values)
        return sum((x - mean) ** 2 for x in values) / len(values)

    def _calculate_std_dev(self, values: List[float]) -> float:
        """Calculate standard deviation of numeric values."""
        import math

        return math.sqrt(self._calculate_variance(values))

    def _flatten_results(self, results: List[Any]) -> List[Any]:
        """Flatten nested results."""
        flattened = []
        for result in results:
            if isinstance(result, list):
                flattened.extend(result)
            else:
                flattened.append(result)
        return flattened

    def _group_results(self, results: List[Any], key: str) -> Dict[str, List[Any]]:
        """Group results by a key."""
        if not key:
            return {"all": results}

        groups = {}
        for result in results:
            if isinstance(result, dict) and key in result:
                group_key = str(result[key])
                if group_key not in groups:
                    groups[group_key] = []
                groups[group_key].append(result)
        return groups

    def _filter_results(self, results: List[Any], condition: str) -> List[Any]:
        """Filter results based on condition."""
        if not condition:
            return results

        filtered = []
        for result in results:
            try:
                context = {"result": result, "item": result}
                if eval(condition, {"__builtins__": {}}, context):
                    filtered.append(result)
            except Exception:
                continue
        return filtered

    def _transform_results(self, results: List[Any], transformation: str) -> List[Any]:
        """Transform results using expression."""
        if not transformation:
            return results

        transformed = []
        for result in results:
            try:
                context = {"result": result, "item": result}
                transformed_item = eval(transformation, {"__builtins__": {}}, context)
                transformed.append(transformed_item)
            except Exception:
                transformed.append(result)
        return transformed
