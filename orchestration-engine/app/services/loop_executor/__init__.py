"""
Loop Executor Service Package

This package provides loop execution capabilities for the orchestration engine,
including state management, result aggregation, and concurrent execution.

Main Components:
- LoopExecutor: Main loop execution engine
- LoopStateManager: State management for loop execution
- LoopAggregator: Result aggregation strategies
- LoopValidator: Schema validation for loop configurations
"""

from .loop_executor import LoopExecutor
from .loop_state_manager import LoopStateManager
from .loop_aggregator import LoopAggregator
from .loop_validator import LoopValidator

__all__ = [
    "LoopExecutor",
    "LoopStateManager", 
    "LoopAggregator",
    "LoopValidator"
]

__version__ = "1.0.0"
