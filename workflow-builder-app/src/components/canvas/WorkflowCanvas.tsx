
import React, { useState, use<PERSON><PERSON>back, useRef, useEffect } from "react";
import React<PERSON>low, {
  ReactFlowProvider,
  addEdge,
  applyNodeChanges,
  applyEdgeChanges,
  Node,
  Edge,
  OnNodesChange,
  OnEdgesChange,
  OnConnect,
  NodeChange,
  EdgeChange,
  Connection,
  Background,
  Controls,
  MiniMap,
  useReactFlow,
  NodeOrigin,
  BackgroundVariant,
  Panel,
} from "reactflow";
import { Trash2, Co<PERSON>, Clipboard } from "lucide-react";

import { WorkflowNodeData, ComponentDefinition } from "@/types";
import { InspectorPanel } from "@/components/inspector";
import WorkflowNode from "@/components/nodes/WorkflowNode";
import DeletableEdge from "@/components/edges/DeletableEdge";
import { getConnectedNodesWithToolConnections } from "@/lib/validation/toolConnectionFlow";
import { isToolHandle } from "@/utils/toolConnectionUtils";

// Style for minimap with more modern colors
const nodeColor = (node: Node) => {
  const data = node.data as WorkflowNodeData;
  const category = data?.definition?.category?.toLowerCase() || "";

  // Color nodes based on their category
  switch (category) {
    case "io":
    case "input/output": // Add alias for consistency
      return "var(--chart-1)";
    case "data":
      return "var(--chart-2)";
    case "processing":
      return "var(--chart-3)";
    case "api":
      return "var(--chart-4)";
    case "logic": // Add category for LoopNode
    case "control flow":
      return "var(--chart-5)";
    default:
      return "var(--primary)";
  }
};

// Create a StartNode as the initial node
const createStartNode = (): Node<WorkflowNodeData> => {
  return {
    id: "start-node",
    type: "WorkflowNode",
    position: { x: 100, y: 100 },
    data: {
      label: "Start",
      type: "component",
      originalType: "StartNode",
      definition: {
        name: "StartNode",
        display_name: "Start",
        description:
          "The starting point for all workflows. Only nodes connected to this node will be executed.",
        category: "Input/Output",
        icon: "Play",
        beta: false,
        inputs: [],
        outputs: [
          {
            name: "flow",
            display_name: "Flow",
            output_type: "Any",
          },
        ],
        is_valid: true,
        path: "components.io.start_node",
      },
      config: {
        collected_parameters: {},
      },
    },
  };
};

const initialNodes: Node<WorkflowNodeData>[] = [createStartNode()];
const initialEdges: Edge[] = [];

// Set node origin to center for better drag/drop placement
const nodeOrigin: NodeOrigin = [0, 0];

// Define nodeTypes outside the component to prevent re-creation on each render
const nodeTypes = {
  WorkflowNode: WorkflowNode, // Register custom node with a key
};

// Define edgeTypes outside the component to prevent re-creation on each render
const edgeTypes = {
  default: DeletableEdge,
};

interface ApplicationData {
  providers: any[];
  models: any[];
  credentials: any[];
  isLoadingAppData: boolean;
  appDataError: string | null;
}

interface FlowCanvasProps {
  onFlowChange: (nodes: Node<WorkflowNodeData>[], edges: Edge[]) => void;
  initialNodes?: Node<WorkflowNodeData>[];
  initialEdges?: Edge[];
  applicationData?: ApplicationData;
}

function FlowCanvas({
  onFlowChange,
  initialNodes: propInitialNodes,
  initialEdges: propInitialEdges,
  applicationData,
}: FlowCanvasProps) {
  const reactFlowWrapper = useRef<HTMLDivElement>(null);
  const { screenToFlowPosition, fitView } = useReactFlow();

  const [nodes, setNodes] = useState<Node<WorkflowNodeData>[]>(propInitialNodes || initialNodes);
  const [edges, setEdges] = useState<Edge[]>(() => {
    const initialEdgeState = propInitialEdges || initialEdges;
    return initialEdgeState.map((edge) => ({
      ...edge,
      type: edge.type || "default",
    }));
  });
  const [selectedNode, setSelectedNode] = useState<Node<WorkflowNodeData> | null>(null);

  // Update nodes and edges when initialNodes or initialEdges change
  useEffect(() => {
    if (propInitialNodes) {
      const hasStartNode = propInitialNodes.some((node) => node.data.originalType === "StartNode");
      setNodes(hasStartNode ? propInitialNodes : [createStartNode(), ...propInitialNodes]);
    }
  }, [propInitialNodes]);

  useEffect(() => {
    if (propInitialEdges) {
      const edgesWithType = propInitialEdges.map((edge) => ({
        ...edge,
        type: edge.type || "default",
      }));
      setEdges(edgesWithType);
    }
  }, [propInitialEdges]);

  const memoizedNodes = useRef(nodes);
  const memoizedEdges = useRef(edges);
  const isEditingFieldRef = useRef(false);

  const setIsEditingField = useCallback((isEditing: boolean) => {
    isEditingFieldRef.current = isEditing;
  }, []);

  useEffect(() => {
    if (
      (nodes !== memoizedNodes.current || edges !== memoizedEdges.current) &&
      !isEditingFieldRef.current
    ) {
      memoizedNodes.current = nodes;
      memoizedEdges.current = edges;
      onFlowChange(nodes, edges);
    } else if (isEditingFieldRef.current) {
      memoizedNodes.current = nodes;
      memoizedEdges.current = edges;
    }
  }, [nodes, edges, onFlowChange]);

  useEffect(() => {
    if (nodes.length > 0) {
      setTimeout(() => fitView({ padding: 0.2 }), 100);
    }
  }, [nodes, fitView]);

  const getConnectedNodes = useCallback(
    (sourceNodeId: string, edgesList: Edge[], visited = new Set<string>()): Set<string> => {
      if (visited.has(sourceNodeId)) return visited;
      visited.add(sourceNodeId);
      const outgoingEdges = edgesList.filter((edge) => edge.source === sourceNodeId);
      for (const edge of outgoingEdges) {
        getConnectedNodes(edge.target, edgesList, visited);
      }
      return visited;
    },
    [],
  );

  const getNodesConnectedToStartNode = useCallback((): Set<string> => {
    const startNode = nodes.find((node) => node.data.originalType === "StartNode");
    if (!startNode) return new Set<string>();
    return getConnectedNodesWithToolConnections(nodes, edges, startNode.id);
  }, [nodes, edges]);

  const updateNodeStyling = useCallback(() => {
    if (nodes.length === 0) return;
    const connectedNodes = getNodesConnectedToStartNode();
    setNodes((nds) => {
      const needsUpdate = nds.some((node) => {
        if (node.data.originalType === "StartNode") return false;
        const isConnected = connectedNodes.has(node.id);
        const currentOpacity = node.style?.opacity;
        return (isConnected && currentOpacity !== 1) || (!isConnected && currentOpacity !== 0.5);
      });
      if (!needsUpdate) return nds;
      return nds.map((node) => {
        if (node.data.originalType === "StartNode") return node;
        const isConnected = connectedNodes.has(node.id);
        return {
          ...node,
          style: { ...node.style, opacity: isConnected ? 1 : 0.5 },
        };
      });
    });
  }, [nodes, edges, getNodesConnectedToStartNode]);

  useEffect(() => {
    updateNodeStyling();
  }, [updateNodeStyling]);

  const onNodesChange: OnNodesChange = useCallback((changes: NodeChange[]) => {
    setNodes((nds) => {
      const filteredChanges = changes.filter((change) => {
        if (change.type === "remove") {
          const node = nds.find((n) => n.id === change.id);
          if (node && node.data.originalType === "StartNode") return false;
        }
        return true;
      });
      return applyNodeChanges(filteredChanges, nds);
    });
  }, []);

  const onEdgesChange: OnEdgesChange = useCallback(
    (changes: EdgeChange[]) => setEdges((eds) => applyEdgeChanges(changes, eds)),
    [],
  );

  const onConnect: OnConnect = useCallback(
    (connection: Connection) => setEdges((eds) => addEdge({ ...connection, type: "default" }, eds)),
    [setEdges],
  );

  const onDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = "move";
  }, []);

  const onDrop = useCallback(
    (event: React.DragEvent) => {
      event.preventDefault();
      if (!reactFlowWrapper.current) return;

      const dataString = event.dataTransfer.getData("application/reactflow");
      if (!dataString) return;

      // Define a more specific type for the parsed data, including optional loop_config
      const { nodeType: originalNodeType, definition } = JSON.parse(dataString) as {
        nodeType: string;
        definition: ComponentDefinition & { loop_config?: any };
      };

      const bounds = reactFlowWrapper.current.getBoundingClientRect();
      const position = screenToFlowPosition({
        x: event.clientX - bounds.left,
        y: event.clientY - bounds.top,
      });

      const isMCPComponent = originalNodeType.includes("MCP_");
      const isAgentComponent = originalNodeType === "AgenticAI";
      const isLoopNode = originalNodeType === "LoopNode";
      let nodeType = "component";
      if (isMCPComponent) nodeType = "mcp";
      else if (isAgentComponent) nodeType = "agent";
      else if (isLoopNode) nodeType = "loop";

      // ======================= START: FIX IMPLEMENTATION =======================
      // Initialize config. For any node providing a default config in its
      // definition (like LoopNode), copy it into the node's instance data.
      const initialConfig: { [key: string]: any } = {};

      if (definition.loop_config) {
        // Deep copy to prevent reference issues if the definition object is reused.
        initialConfig.loop_config = JSON.parse(JSON.stringify(definition.loop_config));
        console.log("Initialized LoopNode with default config:", initialConfig.loop_config);
      }
      // This pattern is extensible for other components in the future:
      // if (definition.another_default_config) {
      //   initialConfig.another_default_config = JSON.parse(JSON.stringify(definition.another_default_config));
      // }
      // ======================== END: FIX IMPLEMENTATION ========================

      const newNode: Node<WorkflowNodeData> = {
        id: `${originalNodeType}-${+new Date()}`,
        type: "WorkflowNode",
        position,
        data: {
          label: definition.display_name,
          type: nodeType,
          originalType: originalNodeType,
          definition: definition,
          // Use the prepared initialConfig object instead of an empty one.
          config: initialConfig,
        },
      };

      setNodes((nds) => nds.concat(newNode));
    },
    [screenToFlowPosition, setNodes],
  );

  const onNodeClick = useCallback((_: React.MouseEvent, node: Node<WorkflowNodeData>) => {
    setSelectedNode(node);
  }, []);

  const handleCloseInspector = () => setSelectedNode(null);

  const handleDeleteNode = useCallback(
    (nodeId: string) => {
      const node = nodes.find((n) => n.id === nodeId);
      if (node && node.data.originalType === "StartNode") return;

      setNodes((nds) => nds.filter((n) => n.id !== nodeId));
      setEdges((eds) => eds.filter((e) => e.source !== nodeId && e.target !== nodeId));
      setSelectedNode(null);
    },
    [nodes, setNodes, setEdges],
  );

  const handleNodeDataChange = useCallback(
    (nodeId: string, newData: WorkflowNodeData) => {
      setNodes((nds) =>
        nds.map((node) => (node.id === nodeId ? { ...node, data: newData } : node)),
      );
      setSelectedNode((prev) => (prev && prev.id === nodeId ? { ...prev, data: newData } : prev));
    },
    [setNodes],
  );

  useEffect(() => {
    const handleKeyDown = (event: globalThis.KeyboardEvent) => {
      if (
        (event.key === "Delete" || event.key === "Backspace") &&
        selectedNode &&
        !(event.target instanceof HTMLInputElement) &&
        !(event.target instanceof HTMLTextAreaElement)
      ) {
        handleDeleteNode(selectedNode.id);
      }
    };
    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [selectedNode, handleDeleteNode]);

  const [copiedNode, setCopiedNode] = useState<Node<WorkflowNodeData> | null>(null);

  const handleCopyNode = useCallback(() => {
    if (selectedNode) setCopiedNode(selectedNode);
  }, [selectedNode]);

  const handlePasteNode = useCallback(() => {
    if (!copiedNode) return;

    const originalType = copiedNode.data.originalType || copiedNode.data.type;
    const newId = `${originalType}-${+new Date()}`;
    const newPosition = { x: copiedNode.position.x + 50, y: copiedNode.position.y + 50 };

    // ======================= START: ROBUSTNESS FIX FOR PASTE =======================
    // Re-apply the default config logic to ensure pasted nodes are also correct,
    // even if they were copied from an older, buggy state.
    const definition = copiedNode.data.definition as ComponentDefinition & { loop_config?: any };
    const newConfig = { ...(copiedNode.data.config || {}) };

    // If the pasted node is a LoopNode but is missing its config, add the default.
    if (originalType === "LoopNode" && !newConfig.loop_config && definition?.loop_config) {
      newConfig.loop_config = JSON.parse(JSON.stringify(definition.loop_config));
      console.log("Fixed missing loop_config on pasted node:", newId);
    }
    // ======================== END: ROBUSTNESS FIX FOR PASTE ========================

    const newNode: Node<WorkflowNodeData> = {
      ...copiedNode,
      id: newId,
      position: newPosition,
      selected: false,
      // Use a deep copy of the data and apply the potentially fixed config
      data: {
        ...copiedNode.data,
        config: newConfig,
      },
    };

    setNodes((nds) => nds.concat(newNode));
  }, [copiedNode, setNodes]);

  return (
    <div className="relative h-full w-full flex-grow overflow-hidden" ref={reactFlowWrapper}>
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        onDrop={onDrop}
        onDragOver={onDragOver}
        onNodeClick={onNodeClick}
        onPaneClick={handleCloseInspector}
        nodeTypes={nodeTypes}
        edgeTypes={edgeTypes}
        fitView
        nodeOrigin={nodeOrigin}
        className="bg-background"
        snapToGrid={true}
        snapGrid={[20, 20]}
        defaultEdgeOptions={{
          animated: true,
          style: { strokeWidth: 2, stroke: "var(--primary)", zIndex: 5 },
        }}
      >
        <Controls showInteractive={false} className="bg-card rounded-lg border p-1 shadow-md" />
        <MiniMap
          nodeStrokeWidth={3}
          zoomable
          pannable
          nodeColor={nodeColor}
          className="bg-card/80 rounded-lg border shadow-md backdrop-blur-sm"
        />
        <Background
          variant={BackgroundVariant.Dots}
          gap={20}
          size={1}
          color="var(--border)"
          className="bg-background/50"
        />
      </ReactFlow>

      <InspectorPanel
        selectedNode={selectedNode}
        onNodeDataChange={handleNodeDataChange}
        onClose={handleCloseInspector}
        onDeleteNode={handleDeleteNode}
        edges={edges}
        nodes={nodes}
        setIsEditingField={setIsEditingField}
        applicationData={applicationData}
      />
    </div>
  );
}

// Wrapper component updated to pass application data
const WorkflowCanvasWrapper = React.memo(function WorkflowCanvasWrapper({
  onFlowChange,
  initialWorkflow,
  applicationData,
}: {
  onFlowChange: (nodes: Node<WorkflowNodeData>[], edges: Edge[]) => void;
  initialWorkflow?: { nodes: Node<WorkflowNodeData>[]; edges: Edge[] };
  applicationData?: ApplicationData;
}) {
  return (
    <ReactFlowProvider>
      <FlowCanvas
        onFlowChange={onFlowChange}
        initialNodes={initialWorkflow?.nodes}
        initialEdges={initialWorkflow?.edges}
        applicationData={applicationData}
      />
    </ReactFlowProvider>
  );
});

export default WorkflowCanvasWrapper;