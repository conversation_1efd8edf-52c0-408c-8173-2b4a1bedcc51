import React, { createContext, useContext, ReactNode } from "react";
import { Node, Edge } from "reactflow";
import { WorkflowNodeData } from "@/types";
import { useInspectorStore } from "@/store/inspectorStore";
import { useConnectedHandles } from "@/hooks/useConnectedHandles";
import { useComponentStateStore } from "@/store/mcpToolsStore";
import { populateDefaultValues } from "@/utils/inputValueUtils";

// Define the context type
interface InspectorContextType {
  // Node data
  selectedNode: Node<WorkflowNodeData> | null;

  // UI state from inspector store
  activeTab: "settings" | "info" | "advanced";
  setActiveTab: (tab: "settings" | "info" | "advanced") => void;
  showValidation: boolean;
  setShowValidation: (show: boolean) => void;
  validationErrors: Record<string, { isValid: boolean; message: string }>;
  setValidationError: (inputName: string, error: { isValid: boolean; message: string }) => void;
  clearValidationErrors: () => void;

  // Connection utilities
  isInputConnected: (inputName: string) => boolean;
  shouldDisableInput: (inputName: string) => boolean;
  getConnectionInfo: (inputName: string) => {
    isConnected: boolean;
    sourceNodeId?: string;
    sourceNodeLabel?: string;
  };

  // Node modification handlers
  handleLabelChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleConfigChange: (inputName: string, value: any) => void;
  handleDefinitionChange: (propertyName: string, value: any) => void;

  // Apply changes explicitly
  applyAllChanges: () => void;

  // Panel actions
  onClose: () => void;
  onDeleteNode: (nodeId: string) => void;
  validateAllNodeInputs: () => void;


  // Additional data for tool management
  nodes: Node<WorkflowNodeData>[];
  edges: Edge[];

  // Notification state
  notification: {
    isOpen: boolean;
    title: string;
    message: string;
    preserveState?: boolean;
  };
  setNotification: React.Dispatch<React.SetStateAction<{
    isOpen: boolean;
    title: string;
    message: string;
    preserveState?: boolean;
  }>>;

  // Application data
  applicationData?: {
    providers: any[];
    models: any[];
    credentials: any[];
    isLoadingAppData: boolean;
    appDataError: string | null;
  };
}

// Create the context with a default undefined value
const InspectorContext = createContext<InspectorContextType | undefined>(undefined);

interface ApplicationData {
  providers: any[];
  models: any[];
  credentials: any[];
  isLoadingAppData: boolean;
  appDataError: string | null;
}

// Provider component
interface InspectorProviderProps {
  children: ReactNode;
  selectedNode: Node<WorkflowNodeData> | null;
  onNodeDataChange: (nodeId: string, newData: WorkflowNodeData) => void;
  onClose: () => void;
  onDeleteNode: (nodeId: string) => void;
  edges: Edge[];
  nodes: Node<WorkflowNodeData>[];
  setIsEditingField?: (isEditing: boolean) => void; // Optional callback to notify when editing fields
  applicationData?: ApplicationData; // Application data for providers, models, credentials
}

export function InspectorProvider({
  children,
  selectedNode,
  onNodeDataChange,
  onClose,
  onDeleteNode,
  edges,
  nodes,
  setIsEditingField,
  applicationData,
}: InspectorProviderProps) {
  // Get state from inspector store
  const {
    activeTab,
    setActiveTab,
    showValidation,
    setShowValidation,
    validationErrors,
    setValidationError,
    clearValidationErrors,
  } = useInspectorStore();

  // Use the connected handles hook
  const { isInputConnected, shouldDisableInput, getConnectionInfo } = useConnectedHandles(
    selectedNode,
    edges,
    nodes
  );

  // Notification state
  const [notification, setNotification] = React.useState<{
    isOpen: boolean;
    title: string;
    message: string;
    preserveState?: boolean;
  }>({ isOpen: false, title: "", message: "", preserveState: true });

  // Effect to populate default values when a node is first selected
  React.useEffect(() => {
    if (selectedNode && selectedNode.data.definition?.inputs) {
      // Check if the node config is missing default values
      const currentConfig = selectedNode.data.config || {};
      const configWithDefaults = populateDefaultValues(selectedNode);

      // Only update if there are new default values to add
      const hasNewDefaults = Object.keys(configWithDefaults).some(
        key => currentConfig[key] === undefined && configWithDefaults[key] !== undefined
      );

      if (hasNewDefaults) {
        const newData: WorkflowNodeData = {
          ...selectedNode.data,
          config: configWithDefaults,
        };

        if (process.env.NODE_ENV === 'development') {
          console.log(`Populating default values for node ${selectedNode.id}:`, configWithDefaults);
        }

        onNodeDataChange(selectedNode.id, newData);
      }
    }
  }, [selectedNode?.id]); // Only run when the selected node changes

  // Handler for label changes
  const handleLabelChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!selectedNode) return;

    // Notify that we're starting to edit a field
    if (setIsEditingField) {
      setIsEditingField(true);
    }

    const newData: WorkflowNodeData = {
      ...selectedNode.data,
      label: e.target.value,
    };

    onNodeDataChange(selectedNode.id, newData);

    // Immediately update the window.currentWorkflowNodes to ensure Run button has access to latest values
    if (typeof window !== 'undefined' && window.currentWorkflowNodes) {
      const nodeIndex = window.currentWorkflowNodes.findIndex((n: any) => n.id === selectedNode.id);
      if (nodeIndex !== -1) {
        // Create a deep copy to avoid reference issues
        const updatedNodes = [...window.currentWorkflowNodes];
        updatedNodes[nodeIndex] = {
          ...updatedNodes[nodeIndex],
          data: {
            ...updatedNodes[nodeIndex].data,
            label: e.target.value
          }
        };
        window.currentWorkflowNodes = updatedNodes;

        if (process.env.NODE_ENV === 'development') {
          console.log(`Updated window.currentWorkflowNodes with new label for node ${selectedNode.id}`);
        }
      }
    }

    // Notify that we're done editing immediately without delay
    if (setIsEditingField) {
      setIsEditingField(false); // Remove timeout to ensure immediate state update
    }
  };

  // Track the last config update to prevent circular updates
  const lastConfigUpdateRef = React.useRef<{
    nodeId: string;
    inputName: string;
    value: any;
    timestamp: number;
  } | null>(null);

  // Handler for configuration changes
  const handleConfigChange = (inputName: string, value: any) => {
    if (!selectedNode) return;

    // Notify that we're starting to edit a field
    if (setIsEditingField) {
      setIsEditingField(true);
    }

    // Check for duplicate/circular updates - but with a much smaller threshold
    // to ensure more immediate updates
    const now = Date.now();
    const lastUpdate = lastConfigUpdateRef.current;

    // If we have a very recent update (within 10ms) with the same node, input and value, skip it
    // Reduced from 50ms to 10ms for more immediate updates
    if (
      lastUpdate &&
      lastUpdate.nodeId === selectedNode.id &&
      lastUpdate.inputName === inputName &&
      JSON.stringify(lastUpdate.value) === JSON.stringify(value) &&
      now - lastUpdate.timestamp < 10
    ) {
      if (process.env.NODE_ENV === 'development') {
        console.log(`Skipping duplicate config update for ${inputName} (debounced)`);
      }
      return;
    }

    // Record this update
    lastConfigUpdateRef.current = {
      nodeId: selectedNode.id,
      inputName,
      value,
      timestamp: now,
    };

    // Special handling for MCP Marketplace components
    if (selectedNode.data.type === "mcp") {
      // Get the current component state
      const stateStore = useComponentStateStore.getState();
      const currentState = stateStore.getValue(selectedNode.id, "config", {});

      // Check if the value is actually changing
      if (JSON.stringify(currentState[inputName]) === JSON.stringify(value)) {
        if (process.env.NODE_ENV === 'development') {
          console.log(`Value for ${inputName} hasn't changed in component state, skipping update`);
        }

        // Notify that we're done editing
        if (setIsEditingField) {
          setIsEditingField(false); // Immediate state update
        }
        return;
      }

      // Update the component state directly
      const newConfig = {
        ...currentState,
        [inputName]: value,
      };
      stateStore.setValue(selectedNode.id, "config", newConfig);

      if (process.env.NODE_ENV === 'development') {
        console.log(`Updated component state for ${selectedNode.id}, input ${inputName}:`, value);
      }
    }

    // Update the node config
    const currentData = selectedNode.data;

    // Check if the value is actually changing in the node data
    if (currentData.config && JSON.stringify(currentData.config[inputName]) === JSON.stringify(value)) {
      if (process.env.NODE_ENV === 'development') {
        console.log(`Value for ${inputName} hasn't changed in node data, skipping update`);
      }

      // Notify that we're done editing
      if (setIsEditingField) {
        setIsEditingField(false); // Immediate state update
      }
      return;
    }

    // Ensure we have all default values populated before adding the new value
    const configWithDefaults = populateDefaultValues(selectedNode);

    const newConfig = {
      ...configWithDefaults,
      [inputName]: value,
    };

    // Create the complete updated data object
    const newData: WorkflowNodeData = {
      ...currentData,
      config: newConfig,
    };

    if (process.env.NODE_ENV === 'development') {
      console.log(`Updating node ${selectedNode.id} with new config for ${inputName}:`, value);
    }

    // Update the node data
    onNodeDataChange(selectedNode.id, newData);

    // Immediately update the window.currentWorkflowNodes to ensure Run button has access to latest values
    if (typeof window !== 'undefined' && window.currentWorkflowNodes) {
      const nodeIndex = window.currentWorkflowNodes.findIndex((n: any) => n.id === selectedNode.id);
      if (nodeIndex !== -1) {
        // Create a deep copy to avoid reference issues
        const updatedNodes = [...window.currentWorkflowNodes];
        updatedNodes[nodeIndex] = {
          ...updatedNodes[nodeIndex],
          data: {
            ...updatedNodes[nodeIndex].data,
            config: {
              ...updatedNodes[nodeIndex].data.config,
              [inputName]: value
            }
          }
        };
        window.currentWorkflowNodes = updatedNodes;

        if (process.env.NODE_ENV === 'development') {
          console.log(`Updated window.currentWorkflowNodes with new config for node ${selectedNode.id}, input ${inputName}`);
        }
      }
    }

    // Notify that we're done editing immediately without delay
    if (setIsEditingField) {
      setIsEditingField(false); // Immediate state update
    }
  };

  // Handler for definition changes
  const handleDefinitionChange = (propertyName: string, value: any) => {
    if (!selectedNode || !selectedNode.data.definition) return;

    // Notify that we're starting to edit a field
    if (setIsEditingField) {
      setIsEditingField(true);
    }

    const newDefinition = {
      ...selectedNode.data.definition,
      [propertyName]: value,
    };

    const newData: WorkflowNodeData = {
      ...selectedNode.data,
      definition: newDefinition,
    };

    onNodeDataChange(selectedNode.id, newData);

    // Immediately update the window.currentWorkflowNodes to ensure Run button has access to latest values
    if (typeof window !== 'undefined' && window.currentWorkflowNodes) {
      const nodeIndex = window.currentWorkflowNodes.findIndex((n: any) => n.id === selectedNode.id);
      if (nodeIndex !== -1) {
        // Create a deep copy to avoid reference issues
        const updatedNodes = [...window.currentWorkflowNodes];
        updatedNodes[nodeIndex] = {
          ...updatedNodes[nodeIndex],
          data: {
            ...updatedNodes[nodeIndex].data,
            definition: newDefinition
          }
        };
        window.currentWorkflowNodes = updatedNodes;

        if (process.env.NODE_ENV === 'development') {
          console.log(`Updated window.currentWorkflowNodes with new definition for node ${selectedNode.id}, property ${propertyName}`);
        }
      }
    }

    // Notify that we're done editing immediately without delay
    if (setIsEditingField) {
      setIsEditingField(false); // Remove timeout to ensure immediate state update
    }
  };

  // Function to explicitly apply all changes
  const applyAllChanges = () => {
    if (!selectedNode) return;

    // Show a notification that changes have been applied
    setNotification({
      isOpen: true,
      title: "Changes Applied",
      message: "All changes have been applied to the node configuration.",
      preserveState: false,
    });

    // Force an update to the global state to ensure Run button has the latest values
    if (typeof window !== 'undefined' && window.currentWorkflowNodes) {
      const nodeIndex = window.currentWorkflowNodes.findIndex((n: any) => n.id === selectedNode.id);
      if (nodeIndex !== -1) {
        // Create a deep copy to avoid reference issues
        const updatedNodes = [...window.currentWorkflowNodes];
        updatedNodes[nodeIndex] = {
          ...selectedNode
        };
        window.currentWorkflowNodes = updatedNodes;

        if (process.env.NODE_ENV === 'development') {
          console.log(`Applied all changes to node ${selectedNode.id} in window.currentWorkflowNodes`);
        }
      }
    }
  };

  // Validate all inputs in the node
  const validateAllNodeInputs = () => {
    if (!selectedNode || !selectedNode.data.definition?.inputs) return;

    // Import validation utility dynamically to avoid circular dependencies
    import("@/utils/inputValidation").then(({ validateAllInputs }) => {
      const results = validateAllInputs(
        selectedNode.data.definition?.inputs || [],
        selectedNode.data.config || {}
      );

      // Update validation state
      clearValidationErrors();
      Object.entries(results).forEach(([inputName, result]) => {
        setValidationError(inputName, result);
      });

      // Show validation UI
      setShowValidation(true);

      // Check if all inputs are valid
      const allValid = Object.values(results).every((result) => result.isValid);

      // Show notification
      setNotification({
        isOpen: true,
        title: allValid ? "Validation Successful" : "Validation Failed",
        message: allValid
          ? "All inputs are valid."
          : "Some inputs are invalid. Please check the highlighted fields. Validation only occurs when explicitly requested or before saving/executing the workflow.",
        preserveState: !allValid, // Keep validation state visible if there are errors
      });
    });
  };


  // Create the context value
  const contextValue: InspectorContextType = {
    selectedNode,
    activeTab,
    setActiveTab,
    showValidation,
    setShowValidation,
    validationErrors,
    setValidationError,
    clearValidationErrors,
    isInputConnected,
    shouldDisableInput,
    getConnectionInfo,
    handleLabelChange,
    handleConfigChange,
    handleDefinitionChange,
    applyAllChanges,
    onClose,
    onDeleteNode,
    validateAllNodeInputs,
    nodes,
    edges,
    notification,
    setNotification,
    applicationData,
  };

  return (
    <InspectorContext.Provider value={contextValue}>
      {children}
    </InspectorContext.Provider>
  );
}

// Custom hook to use the inspector context
export function useInspector() {
  const context = useContext(InspectorContext);
  if (context === undefined) {
    throw new Error("useInspector must be used within an InspectorProvider");
  }
  return context;
}
