"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { AlertCircle, Clock, Calendar, Activity } from "lucide-react";
import {
  getCachedCredentials,
  credentialOperations,
} from "@/lib/credentialEnhancer";
import type {
  Credential,
  CredentialCreate,
  CredentialError,
} from "@/types/credentials";
import { getErrorMessage } from "@/utils/credentialErrorHandler";
import { formatCredentialForDisplay } from "@/utils/credentialTransforms";

interface CredentialFormData {
  name: string;
  description: string;
  value: string;
}

const CredentialManager: React.FC = () => {
  const [credentials, setCredentials] = useState<Credential[]>([]);
  const [loading, setLoading] = useState(true);
  const [creating, setCreating] = useState(false);
  const [deleting, setDeleting] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState<CredentialFormData>({
    name: "",
    description: "",
    value: "",
  });

  // Fetch credentials on component mount
  useEffect(() => {
    loadCredentials();
  }, []);

  const loadCredentials = async () => {
    setLoading(true);
    setError(null);
    try {
      // Use cached credentials instead of direct API call
      const credentials = await getCachedCredentials();
      setCredentials(credentials || []);
    } catch (err) {
      const errorMessage = err && typeof err === 'object' && 'message' in err
        ? getErrorMessage(err as CredentialError)
        : err instanceof Error
        ? err.message
        : "An unknown error occurred";
      setError(errorMessage);
      console.error("Error fetching credentials:", err);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setCreating(true);
    setError(null);
    try {
      // Use credential operations that automatically refresh cache
      await credentialOperations.create(formData);

      // Reset form and refresh credentials from cache
      setFormData({
        name: "",
        description: "",
        value: "",
      });
      await loadCredentials();
    } catch (err) {
      const errorMessage = err && typeof err === 'object' && 'message' in err
        ? getErrorMessage(err as CredentialError)
        : err instanceof Error
        ? err.message
        : "An unknown error occurred";
      setError(errorMessage);
      console.error("Error creating credential:", err);
    } finally {
      setCreating(false);
    }
  };

  const handleDelete = async (credential: Credential) => {
    if (!window.confirm(`Are you sure you want to delete credential "${credential.name}"?`)) {
      return;
    }

    setDeleting(credential.id);
    setError(null);
    try {
      // Use credential operations that automatically refresh cache
      await credentialOperations.delete(credential.id);

      // Refresh credentials from cache
      await loadCredentials();
    } catch (err) {
      const errorMessage = err && typeof err === 'object' && 'message' in err
        ? getErrorMessage(err as CredentialError)
        : err instanceof Error
        ? err.message
        : "An unknown error occurred";
      setError(errorMessage);
      console.error("Error deleting credential:", err);
    } finally {
      setDeleting(null);
    }
  };

  return (
    <div className="p-4">
      <h1 className="mb-4 text-2xl font-bold">Credential Manager</h1>

      {error && (
        <div className="bg-destructive/10 border-destructive text-destructive mb-4 flex items-center gap-2 rounded-md border p-3">
          <AlertCircle className="h-4 w-4" />
          <span>{error}</span>
        </div>
      )}

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Add New Credential</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <label htmlFor="name" className="text-sm font-medium">Name</label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                placeholder="Enter credential name (e.g., 'OpenAI API Key')"
                required
                maxLength={20}
              />
              <p className="text-xs text-muted-foreground">
                Maximum 20 characters. This will be displayed in credential selectors.
              </p>
            </div>

            <div className="space-y-2">
              <label htmlFor="description" className="text-sm font-medium">Description</label>
              <Textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={(e) => setFormData((prev) => ({ ...prev, description: e.target.value }))}
                placeholder="Optional description (e.g., 'Used for content generation workflows')"
                rows={2}
                maxLength={200}
              />
              <p className="text-xs text-muted-foreground">
                Optional. Helps identify the purpose of this credential.
              </p>
            </div>

            <div className="space-y-2">
              <label htmlFor="value" className="text-sm font-medium">Value</label>
              <Input
                id="value"
                name="value"
                type="password"
                value={formData.value}
                onChange={handleInputChange}
                placeholder="Enter the credential value (API key, token, etc.)"
                required
              />
              <p className="text-xs text-muted-foreground">
                This value will be securely stored and only used during workflow execution.
              </p>
            </div>

            <Button type="submit" disabled={creating || loading} className="w-full">
              {creating ? "Adding..." : "Add Credential"}
            </Button>
          </form>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Stored Credentials</CardTitle>
        </CardHeader>
        <CardContent>
          {loading && <p className="text-muted-foreground">Loading credentials...</p>}

          {!loading && credentials.length === 0 && (
            <p className="text-muted-foreground">No credentials found. Add one above.</p>
          )}

          {!loading && credentials.length > 0 && (
            <div className="space-y-4">
              {credentials.map((cred) => (
                <div key={cred.id} className="rounded-lg border p-4 hover:bg-muted/50 transition-colors">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 space-y-2">
                      <div>
                        <h3 className="font-medium text-lg">{cred.name}</h3>
                        {cred.description && (
                          <p className="text-muted-foreground text-sm mt-1">{cred.description}</p>
                        )}
                      </div>

                      <div className="flex flex-wrap gap-4 text-xs text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          <span>Created: {new Date(cred.createdAt).toLocaleDateString()}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          <span>Updated: {new Date(cred.updatedAt).toLocaleDateString()}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Activity className="h-3 w-3" />
                          <span>Last used: {new Date(cred.lastUsedAt).toLocaleDateString()}</span>
                        </div>
                      </div>

                      <div className="text-xs text-muted-foreground">
                        <span>ID: {cred.id}</span>
                      </div>
                    </div>

                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => handleDelete(cred)}
                      disabled={deleting === cred.id || loading}
                    >
                      {deleting === cred.id ? "Deleting..." : "Delete"}
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default CredentialManager;
