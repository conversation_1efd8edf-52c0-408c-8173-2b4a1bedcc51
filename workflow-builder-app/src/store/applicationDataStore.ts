/**
 * Application Data Store
 * 
 * This store manages global application data including providers, models, and credentials.
 * It follows the same pattern as components - loaded once when the application starts
 * and cached for the duration of the session.
 */

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { fetchProviders, fetchModels, Provider, Model } from '@/lib/api';
import { fetchCredentials, Credential } from '@/lib/credentialEnhancer';

/**
 * Application data state interface
 */
interface ApplicationDataState {
  // Data
  providers: Provider[];
  models: Model[];
  credentials: Credential[];
  
  // Loading states
  isLoadingProviders: boolean;
  isLoadingModels: boolean;
  isLoadingCredentials: boolean;
  
  // Error states
  providersError: string | null;
  modelsError: string | null;
  credentialsError: string | null;
  
  // Timestamps for cache management
  providersLoadedAt: number | null;
  modelsLoadedAt: number | null;
  credentialsLoadedAt: number | null;
  
  // Actions
  loadProviders: (force?: boolean) => Promise<void>;
  loadModels: (force?: boolean) => Promise<void>;
  loadCredentials: (force?: boolean) => Promise<void>;
  loadAllData: (force?: boolean) => Promise<void>;
  
  // Utility actions
  refreshProviders: () => Promise<void>;
  refreshModels: () => Promise<void>;
  refreshCredentials: () => Promise<void>;
  clearAllData: () => void;
  
  // Getters
  getProviderById: (id: string) => Provider | undefined;
  getModelById: (id: string) => Model | undefined;
  getCredentialById: (id: string) => Credential | undefined;
  getModelsByProvider: (providerId: string) => Model[];
  getProviderByName: (name: string) => Provider | undefined;
}

// Cache duration: 15 minutes
const CACHE_DURATION = 15 * 60 * 1000;

/**
 * Check if data is still valid based on timestamp
 */
function isCacheValid(timestamp: number | null): boolean {
  if (!timestamp) return false;
  return Date.now() - timestamp < CACHE_DURATION;
}

/**
 * Create the application data store
 */
export const useApplicationDataStore = create<ApplicationDataState>()(
  persist(
    (set, get) => ({
      // Initial state
      providers: [],
      models: [],
      credentials: [],
      
      isLoadingProviders: false,
      isLoadingModels: false,
      isLoadingCredentials: false,
      
      providersError: null,
      modelsError: null,
      credentialsError: null,
      
      providersLoadedAt: null,
      modelsLoadedAt: null,
      credentialsLoadedAt: null,
      
      // Load providers
      loadProviders: async (force = false) => {
        const state = get();
        
        // Skip if already loading
        if (state.isLoadingProviders) return;
        
        // Skip if cache is valid and not forcing
        if (!force && isCacheValid(state.providersLoadedAt) && state.providers.length > 0) {
          console.log('Using cached providers data');
          return;
        }
        
        set({ isLoadingProviders: true, providersError: null });
        
        try {
          console.log('🔄 Loading providers from API...');
          const response = await fetchProviders();
          
          if (response.success) {
            set({
              providers: response.providers,
              providersLoadedAt: Date.now(),
              providersError: null,
            });
            console.log(`✅ Loaded ${response.providers.length} providers`);
          } else {
            throw new Error(response.message || 'Failed to fetch providers');
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          console.error('❌ Failed to load providers:', errorMessage);
          set({ providersError: errorMessage });
        } finally {
          set({ isLoadingProviders: false });
        }
      },
      
      // Load models
      loadModels: async (force = false) => {
        const state = get();
        
        // Skip if already loading
        if (state.isLoadingModels) return;
        
        // Skip if cache is valid and not forcing
        if (!force && isCacheValid(state.modelsLoadedAt) && state.models.length > 0) {
          console.log('Using cached models data');
          return;
        }
        
        set({ isLoadingModels: true, modelsError: null });
        
        try {
          console.log('🔄 Loading models from API...');
          const response = await fetchModels();
          
          if (response.success) {
            set({
              models: response.models,
              modelsLoadedAt: Date.now(),
              modelsError: null,
            });
            console.log(`✅ Loaded ${response.models.length} models`);
          } else {
            throw new Error(response.message || 'Failed to fetch models');
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          console.error('❌ Failed to load models:', errorMessage);
          set({ modelsError: errorMessage });
        } finally {
          set({ isLoadingModels: false });
        }
      },
      
      // Load credentials
      loadCredentials: async (force = false) => {
        const state = get();
        
        // Skip if already loading
        if (state.isLoadingCredentials) return;
        
        // Skip if cache is valid and not forcing
        if (!force && isCacheValid(state.credentialsLoadedAt) && state.credentials.length > 0) {
          console.log('Using cached credentials data');
          return;
        }
        
        set({ isLoadingCredentials: true, credentialsError: null });
        
        try {
          console.log('🔄 Loading credentials from API...');
          const response = await fetchCredentials();
          
          if (response.success) {
            set({
              credentials: response.credentials,
              credentialsLoadedAt: Date.now(),
              credentialsError: null,
            });
            console.log(`✅ Loaded ${response.credentials.length} credentials`);
          } else {
            throw new Error(response.message || 'Failed to fetch credentials');
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          console.error('❌ Failed to load credentials:', errorMessage);
          set({ credentialsError: errorMessage });
        } finally {
          set({ isLoadingCredentials: false });
        }
      },
      
      // Load all data
      loadAllData: async (force = false) => {
        const { loadProviders, loadModels, loadCredentials } = get();
        
        console.log('🚀 Loading all application data...');
        
        // Load all data in parallel
        await Promise.all([
          loadProviders(force),
          loadModels(force),
          loadCredentials(force),
        ]);
        
        console.log('✅ All application data loaded');
      },
      
      // Refresh actions (force reload)
      refreshProviders: async () => {
        await get().loadProviders(true);
      },
      
      refreshModels: async () => {
        await get().loadModels(true);
      },
      
      refreshCredentials: async () => {
        await get().loadCredentials(true);
      },
      
      // Clear all data
      clearAllData: () => {
        set({
          providers: [],
          models: [],
          credentials: [],
          providersLoadedAt: null,
          modelsLoadedAt: null,
          credentialsLoadedAt: null,
          providersError: null,
          modelsError: null,
          credentialsError: null,
        });
        console.log('🗑️ All application data cleared');
      },
      
      // Utility getters
      getProviderById: (id: string) => {
        return get().providers.find(p => p.id === id);
      },
      
      getModelById: (id: string) => {
        return get().models.find(m => m.id === id);
      },
      
      getCredentialById: (id: string) => {
        return get().credentials.find(c => c.id === id);
      },
      
      getModelsByProvider: (providerId: string) => {
        return get().models.filter(m => m.providerId === providerId);
      },
      
      getProviderByName: (name: string) => {
        return get().providers.find(p => p.provider === name);
      },
    }),
    {
      name: 'application-data-store',
      // Only persist the data, not the loading states or errors
      partialize: (state) => ({
        providers: state.providers,
        models: state.models,
        credentials: state.credentials,
        providersLoadedAt: state.providersLoadedAt,
        modelsLoadedAt: state.modelsLoadedAt,
        credentialsLoadedAt: state.credentialsLoadedAt,
      }),
    }
  )
);

/**
 * Hook to get all application data with loading states
 */
export function useApplicationData() {
  const store = useApplicationDataStore();
  
  return {
    // Data
    providers: store.providers,
    models: store.models,
    credentials: store.credentials,
    
    // Loading states
    isLoading: store.isLoadingProviders || store.isLoadingModels || store.isLoadingCredentials,
    isLoadingProviders: store.isLoadingProviders,
    isLoadingModels: store.isLoadingModels,
    isLoadingCredentials: store.isLoadingCredentials,
    
    // Error states
    hasErrors: !!(store.providersError || store.modelsError || store.credentialsError),
    errors: {
      providers: store.providersError,
      models: store.modelsError,
      credentials: store.credentialsError,
    },
    
    // Actions
    loadAllData: store.loadAllData,
    refreshAll: () => store.loadAllData(true),
    
    // Utility functions
    getProviderById: store.getProviderById,
    getModelById: store.getModelById,
    getCredentialById: store.getCredentialById,
    getModelsByProvider: store.getModelsByProvider,
    getProviderByName: store.getProviderByName,
  };
}

export default useApplicationDataStore;
