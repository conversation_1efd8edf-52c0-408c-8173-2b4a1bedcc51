// Quick debug script to test credential API
const axios = require('axios');

async function testCredentialAPI() {
  try {
    console.log('Testing credential API...');
    
    // Test the API endpoint directly
    const response = await axios.get('http://localhost:3000/api/credentials', {
      headers: {
        'ngrok-skip-browser-warning': 'true'
      }
    });
    
    console.log('Response status:', response.status);
    console.log('Response data:', JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.error('Error testing API:', error.response?.data || error.message);
  }
}

testCredentialAPI();
