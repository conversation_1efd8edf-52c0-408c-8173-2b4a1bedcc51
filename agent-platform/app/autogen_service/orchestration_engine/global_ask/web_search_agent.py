"""
WebSearchEmployee - A specialized agent for web search operations using Exa tools.

This module provides a WebSearchEmployee class that handles web search tasks
using the Exa search API. It follows the established agent patterns in the
codebase while focusing specifically on web search functionality.
"""

from __future__ import annotations

import logging
import os
from typing import List, Optional

from autogen_agentchat.agents import AssistantAgent
from autogen_core.models import ChatCompletionClient
from autogen_ext.tools.mcp import StreamableHttpServerParams, mcp_server_tools

from ....shared.config.base import Settings, get_settings
from ...model_factory import ModelFactory

logger = logging.getLogger(__name__)


class WebSearchEmployee:
    """
    A specialized web search agent that uses Exa tools for comprehensive web searching.

    This agent is designed to:
    - Perform targeted web searches using Exa API
    - Process and analyze search results
    - Provide structured, well-formatted responses
    - Verify sources and provide proper citations
    """

    def __init__(self):
        """Initialize the WebSearchEmployee with default configuration."""
        self._settings: Settings = get_settings()
        self._model_client: Optional[ChatCompletionClient] = None
        self._exa_tools: Optional[List] = None
        self._agent: Optional[AssistantAgent] = None
        self._is_initialized: bool = False
        self._exa_api_key: str = os.getenv("EXA_API_KEY")

    async def initialize(self) -> bool:
        """
        Initialize the web search agent with model client and Exa tools.

        Returns:
            bool: True if initialization successful, False otherwise
        """
        try:
            # Create model client using ModelFactory
            model_config = {
                "provider": "OpenAIChatCompletionClient",
                "llm_type": "openai",
                "model": "gpt-4.1-mini-2025-04-14",
                "api_key": self._settings.requesty.api_key,
                "base_url": self._settings.requesty.base_url,
                "model_info": {
                    "vision": False,
                    "function_calling": True,
                    "json_output": True,
                    "structured_output": True,
                },
            }

            self._model_client = ModelFactory.create_model_client(model_config)
            if not self._model_client:
                logger.error("Failed to create model client for WebSearchEmployee")
                return False

            # Initialize Exa tools
            exa_params = StreamableHttpServerParams(
                url=f"https://mcp.exa.ai/mcp?exaApiKey={self._exa_api_key}"
            )
            self._exa_tools = await mcp_server_tools(exa_params)

            # Create the assistant agent
            self._agent = AssistantAgent(
                name="WebSearchEmployee",
                description="Expert web search employee using Exa tools for comprehensive information retrieval and analysis. This employee should be used to find current, accurate information on any topic.",
                model_client=self._model_client,
                tools=self._exa_tools,
                reflect_on_tool_use=True,
                system_message=self._get_enhanced_system_message(),
                # model_client_stream=True,
            )

            self._is_initialized = True
            logger.info("WebSearchEmployee initialized successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize WebSearchEmployee: {str(e)}")
            self._is_initialized = False
            return False

    def _get_enhanced_system_message(self) -> str:
        """
        Get the enhanced system message for the web search agent.

        Returns:
            str: Comprehensive system message for web search operations
        """
        return """
            You are a specialized Web Search Expert with access to powerful Exa search tools.

            CORE RESPONSIBILITIES:
            - Conduct comprehensive web searches for current, accurate information
            - Analyze and synthesize search results from multiple authoritative sources  
            - Provide fact-based responses with proper citations
            - Verify information credibility and cross-reference sources
            - Focus on recent, relevant, and reliable information

            SEARCH STRATEGY:
            1. **Query Optimization**: Craft precise search queries targeting the most relevant information
            2. **Source Diversification**: Search across multiple types of sources (news, academic, official, expert)
            3. **Temporal Awareness**: Prioritize recent information while noting publication dates
            4. **Authority Assessment**: Evaluate source credibility and expertise
            5. **Comprehensive Coverage**: Ensure all aspects of the query are addressed

            EXA TOOLS USAGE:
            - Use your tools to find current information on any topic
            - Leverage different search parameters (content type, date filters, domains)
            - Access and analyze full content from relevant pages
            - Extract key facts, statistics, quotes, and insights
            - Cross-reference information across multiple sources

            RESPONSE FORMATTING:
            - The response should be annotated with citations for all sources.
            - The results should be structured according to the following JSON format containing a list of sources:
            {
                "content": "Your well-structured response here",
                "sources": "The list of sources of the information",
                "urls": "The list of URLs for verification",
                "images": "Optional list of image URLs if applicable",
                "dates": "Optional Publication date of the source",
            }

            CITATION REQUIREMENTS:
            - Always cite sources with publication dates when available
            - Include URLs for verification
            - Note the credibility level of sources (official, expert, news, etc.)
            - Indicate when information conflicts between sources
            - Mention any limitations or gaps in available information

            QUALITY STANDARDS:
            - Cross-verify important claims across multiple sources
            - Acknowledge uncertainty when information is limited
            - Distinguish between facts, opinions, and speculation
            - Provide context for statistics and claims

            CURRENT FOCUS:
            When conducting searches, focus on finding the most current, authoritative, and comprehensive information available. Always verify information through multiple sources and provide proper attribution for all findings.
            """

    def get_agent(self) -> Optional[AssistantAgent]:
        """
        Get the underlying AssistantAgent instance.

        Returns:
            Optional[AssistantAgent]: The agent instance if initialized, None otherwise
        """
        return self._agent if self._is_initialized else None

    def is_initialized(self) -> bool:
        """
        Check if the agent is properly initialized.

        Returns:
            bool: True if initialized, False otherwise
        """
        return self._is_initialized

    @classmethod
    async def create_and_initialize(cls) -> Optional["WebSearchEmployee"]:
        """
        Create and initialize a WebSearchEmployee instance.

        Returns:
            Optional[WebSearchEmployee]: Initialized agent instance or None if failed
        """
        agent = cls()
        if await agent.initialize():
            return agent
        else:
            logger.error("Failed to create and initialize WebSearchEmployee")
            return None
