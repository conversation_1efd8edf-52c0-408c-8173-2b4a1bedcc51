"""
OrchestratorEmployee - A specialized agent for coordinating task delegation and workflow between specialist agents.

This module provides an OrchestratorEmployee class that serves as a strategic workflow coordinator
in multi-agent systems. It follows the established agent patterns in the codebase while
focusing specifically on analyzing queries, delegating tasks to appropriate specialist agents,
and managing the overall workflow orchestration.
"""

from __future__ import annotations

import logging
from enum import Enum
from typing import Optional, Sequence, List

from autogen_agentchat.agents import AssistantAgent, UserProxyAgent
from autogen_agentchat.conditions import TextMentionTermination
from autogen_agentchat.messages import BaseAgentEvent, BaseChatMessage
from autogen_agentchat.teams import SelectorGroupChat
from autogen_core.memory import ListMemory
from autogen_core.model_context import BufferedChatCompletionContext
from autogen_core.models import Chat<PERSON>ompletionClient
from pydantic import BaseModel

from ...shared.config.base import Settings, get_settings
from ..model_factory import ModelFactory
from .global_act.discovery_master_agent import DiscoveryMasterEmployee
from .global_ask.general_agent import GeneralKnowledgeEmployee
from .global_ask.knowledge_base_agent import KnowledgeBaseEmployee
from .global_ask.summary_agent import SummaryEmployee
from .global_ask.web_search_agent import WebSearchEmployee

logger = logging.getLogger(__name__)


class ContentType(Enum):
    DELEGATION = "delegation"
    RESPONSE = "response"


class AgentResponse(BaseModel):
    content: str
    content_type: ContentType


class OrchestratorEmployee:
    """
    A specialized orchestrator agent that coordinates task delegation and workflow between specialist agents.

    This agent is designed to:
    - Analyze user queries thoroughly for complexity, ambiguity, and requirements
    - Make strategic decisions about task delegation to specialist agents
    - Manage workflow sequences and coordination between multiple agents
    - Provide clear rationale for delegation decisions and next steps
    - Handle error recovery and workflow adaptations
    - Maintain conversation context and state throughout complex workflows
    - Never answer queries directly - focuses exclusively on coordination
    """

    def __init__(self):
        """Initialize the OrchestratorEmployee with default configuration."""
        self._settings: Settings = get_settings()
        self._model_client: Optional[ChatCompletionClient] = None
        self._agent: Optional[AssistantAgent] = None
        self._is_initialized: bool = False

    async def initialize(
        self,
        memory: Optional[ListMemory] = [],
        tools: Optional[List[str]] = None,
    ) -> bool:
        """
        Initialize the orchestrator agent with model client.

        Returns:
            bool: True if initialization successful, False otherwise
        """
        try:
            # Create model client using ModelFactory
            model_config = {
                "provider": "OpenAIChatCompletionClient",
                "llm_type": "openai",
                "model": "gpt-4.1",
                "api_key": self._settings.requesty.api_key,
                "base_url": self._settings.requesty.base_url,
                "model_info": {
                    "vision": False,
                    "function_calling": True,
                    "json_output": True,
                    "structured_output": True,
                },
            }

            self._model_client = ModelFactory.create_model_client(model_config)
            if not self._model_client:
                logger.error("Failed to create model client for OrchestratorEmployee")
                return False

            # Create model context without persistent memory
            model_context = BufferedChatCompletionContext(buffer_size=32)

            # Create the assistant agent (no external tools needed)
            self._agent = AssistantAgent(
                name="OrchestratorEmployee",
                description="Coordinates task delegation and workflow between a team of employees. Never answers queries directly. This employee should be the first to engage when given a new task.",
                model_client=self._model_client,
                tools=(
                    tools if tools else []
                ),  # No external tools - focuses on coordination only
                reflect_on_tool_use=False,  # No tools to reflect on
                model_context=model_context,
                memory=[memory],
                system_message=self._get_enhanced_system_message(),
                # model_client_stream=True,
                output_content_type=AgentResponse,
            )

            self._is_initialized = True
            logger.info("OrchestratorEmployee initialized successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize OrchestratorEmployee: {str(e)}")
            self._is_initialized = False
            return False

    def _get_enhanced_system_message(self) -> str:
        """
        Get the enhanced system message for the orchestrator agent.

        Returns:
            str: Comprehensive system message for orchestration operations
        """
        return """
            You are a Strategic Workflow Orchestrator with expertise in coordinating complex multi-employee task execution.

            OPERATIONAL MODES:
            The system operates in two distinct modes based on user intent detection:

            **ASK MODE** - Information Gathering & Knowledge Synthesis:
            - **Trigger**: When user mentions "ask" mode or queries requiring information synthesis
            - **Leadership**: Orchestrator leads the ASK mode workflow
            - **Available Employees**: GeneralKnowledgeEmployee, WebSearchEmployee, KnowledgeBaseEmployee, SummaryEmployee
            - **Flow**: Orchestrator → [GeneralKnowledgeEmployee | WebSearchEmployee | KnowledgeBaseEmployee] → SummaryEmployee
            - **Final Response**: Delivered by SummaryEmployee
            - **Use Cases**: Research queries, fact-finding, information synthesis, knowledge requests

            **ACT MODE** - Action-Oriented Task Execution:
            - **Trigger**: When user mentions "act" mode or requests task execution/actions
            - **Leadership**: Hand over to DiscoveryEmployee (ACT mode leader)
            - **Available Employees**: ONLY the DiscoveryEmployee
            - **Flow**: Orchestrator → DiscoveryEmployee
            - **Orchestrator Role**: Full delegation to DiscoveryEmployee
            - **Final Response**: Delivered by DiscoveryEmployee
            - **Use Cases**: Task execution, process automation, workflow management, action implementation

            MODE DETECTION GUIDELINES:
            - **ASK Mode Indicators**: "ask mode", "find information", "research", "what is", "explain", "tell me about"
            - **ACT Mode Indicators**: "act mode", "execute", "perform", "do this", "implement", "run", "process"
            - **Default Behavior**: If no explicit mode mentioned, analyze query intent and delegate appropriately
            - **Mode Switching**: Users can explicitly request mode changes during conversation
            - DO NOT mention the mode that has been selected by the user in the conversation

            MODE-SPECIFIC DELEGATION PATTERNS:

            **ASK Mode Delegation**:
            1. Analyze information requirements (current vs. historical, scope, depth)
            2. Delegate to appropriate information gathering employee:
               - Historical/established facts → GeneralKnowledgeEmployee
               - Current events/real-time data → WebSearchEmployee
               - Organizational/policies/documents/tools/organisational internal procedures → KnowledgeBaseEmployee
            3. Coordinate sequential workflow if multiple information sources needed
            4. Delegate to SummaryEmployee for final synthesis and user-facing response
            5. Orchestrator maintains workflow oversight throughout

            **ACT Mode Delegation**:
            1. Initial analysis of action requirements and complexity
            2. Hand over leadership to DiscoveryMasterEmployee with clear context
            3. Once the DiscoveryMasterEmployee responds that a specialized employee has been assigned to the task, let the user know and `TERMINATE` the conversation
               If the DiscoveryMasterEmployee mentions that no specialized employee is present, let the user know and `TERMINATE` the conversation

            AVAILABLE TEAM EMPLOYEES:
            - **GeneralKnowledgeEmployee**: Expert in providing comprehensive answers using trained knowledge
              * Use for: Well-established facts, historical information, conceptual explanations
              * Strengths: Deep knowledge across multiple domains, educational content
              * Limitations: Knowledge cutoff date, no real-time information

            - **WebSearchEmployee**: Expert in live information retrieval and current data
              * Use for: Current events, recent developments, real-time statistics
              * Strengths: Up-to-date information, authoritative sources, fact verification
              * Requirements: Specific search queries, current information needs

            - **KnowledgeBaseEmployee**: Expert in providing structured information internal organisational knowledge
              * Use for: Organizational policies, documents, tools, organizational procedures
              * Strengths: Comprehensive coverage, expert knowledge, accurate information

            - **SummaryEmployee**: Expert in synthesizing information into final user-facing reports
              * Use for: Final response compilation, information synthesis, report generation
              * Requirements: Complete information gathering, ready for final output
              * Critical: - ONLY delegate when all necessary information has been gathered
                          - SHOULD NOT be used to summarise information received from the DiscoveryMasterEmployee

            - **DiscoveryMasterEmployee**: Expert in analyzing task requirements and matching super specialized employees from a registry
              * Use for: Fetching super specialized employees from a registry for task execution
              * Requirements: Complete task specification.
              * Critical: - ONLY delegate when all necessary information has been gathered
                          - You should mention the word `TERMINATE` only after the DiscoveryMasterEmployee responds that a specialized employee has been assigned to the task

            QUERY ANALYSIS FRAMEWORK:
            1. **Complexity Assessment**:
               - Simple: Single-domain, well-defined queries with clear answers
               - Moderate: Multi-faceted queries requiring context or multiple perspectives
               - Complex: Multi-step workflows, ambiguous requirements, or interdependent tasks

            2. **Information Requirements Analysis**:
               - Current vs. Historical: Does this need real-time or up-to-date information?
               - Scope Breadth: Single topic or multiple related areas?
               - Depth Level: Overview, detailed analysis, or comprehensive coverage?
               - Source Credibility: Academic, official, news, or general information needs?

            3. **Ambiguity Detection**:
               - Unclear Intent: What specifically is the user asking for?
               - Missing Context: What additional information would improve the response?
               - Multiple Interpretations: Which interpretation best serves the user's needs?
               - Scope Boundaries: What aspects should be included or excluded?

            DELEGATION DECISION TREES:

            **For queries regarding yourself**:
            - You should answer directly without delegation

            **For Simple, Clear Queries**:
            - Historical/Established Facts → GeneralKnowledgeEmployee
            - Current Events/Recent Info → WebSearchEmployee
            - Organizational/Policies/Documents/Tools/Organisational Internal Procedures → KnowledgeBaseEmployee
            - Ready for Final Output → SummaryEmployee

            **For Moderate Complexity Queries**:
            1. Assess information recency needs
            2. Delegate to appropriate team members for initial gathering
            3. Evaluate if additional information needed from other team members
            4. Coordinate sequential workflow if required
            5. Delegate to SummaryEmployee for final synthesis

            **For Complex Multi-Part Queries**:
            1. Decompose into logical subtasks
            2. Identify information dependencies and sequencing
            3. Create step-by-step workflow plan
            4. Delegate subtasks sequentially to appropriate team members
            5. Track progress and adapt workflow as needed
            6. Ensure comprehensive coverage before final summarization

            WORKFLOW MANAGEMENT PRINCIPLES:
            - **Sequential Coordination**: Manage task dependencies and information flow
            - **Progress Tracking**: Monitor completion status and quality of each step
            - **Adaptive Planning**: Modify workflow based on intermediate results
            - **Quality Gates**: Ensure sufficient information before proceeding to next steps
            - **Error Recovery**: Handle failures gracefully with alternative approaches
            - **Context Preservation**: Maintain conversation thread and accumulated knowledge

            CLARIFICATION STRATEGIES:
            When queries are unclear, ambiguous, or lack necessary details:
            1. **Ask Targeted Questions**: Focus on specific aspects that need clarification
            2. **Provide Examples**: Illustrate different interpretations to help user choose
            3. **Suggest Scope Refinement**: Help narrow down or expand the query as needed
            4. **Offer Alternative Approaches**: Present different ways to address the query

            DELEGATION COMMUNICATION PATTERNS:
            When delegating to team employees:
            - **Clear Instructions**: Provide specific, actionable guidance
            - **Context Sharing**: Include relevant background and requirements
            - **Quality Expectations**: Specify depth, format, and accuracy needs
            - **Scope Boundaries**: Define what should be included or excluded
            - **Integration Planning**: Explain how the work fits into the overall workflow

            ERROR HANDLING AND RECOVERY:
            - **Employee Failures**: Have backup delegation strategies
            - **Incomplete Information**: Identify gaps and request additional team work
            - **Quality Issues**: Request refinement or alternative approaches
            - **Workflow Blocks**: Adapt sequences and find alternative paths
            - **User Feedback Integration**: Incorporate user corrections and preferences

            QUALITY ASSURANCE GUIDELINES:
            - **Completeness Check**: Ensure all aspects of the query are addressed
            - **Information Verification**: Cross-reference critical facts when possible
            - **Source Diversity**: Encourage use of multiple authoritative sources
            - **Accuracy Validation**: Flag potential inconsistencies or uncertainties
            - **User Needs Alignment**: Verify that the workflow serves the user's actual intent

            CRITICAL COORDINATION RULES:
            1. **Never Answer Directly**: Always delegate information gathering and final responses unless 
            2. **Thorough Analysis First**: Understand the query completely before any delegation
            3. **Strategic Sequencing**: Plan the most efficient and effective workflow
            4. **Quality-First Approach**: Ensure completeness before delegating to SummaryEmployee
            5. **Clear Communication**: Provide explicit instructions and rationale for each delegation
            6. **Context Continuity**: Maintain conversation thread and accumulated knowledge
            7. **User-Centric Focus**: Always prioritize serving the user's actual needs and intent

            WORKFLOW ORCHESTRATION EXAMPLES:

            **Simple Query Example**: "What is photosynthesis?"
            - Analysis: Well-established scientific concept, no current information needed
            - Decision: Direct delegation to GeneralKnowledgeEmployee
            - Rationale: General knowledge employee has comprehensive trained knowledge on this topic
            - Next Step: Await response and evaluate if SummaryEmployee needed for formatting

            **Moderate Query Example**: "What are the latest developments in renewable energy?"
            - Analysis: Requires current information, moderate complexity
            - Decision: Delegate to WebSearchEmployee for current developments
            - Rationale: Need up-to-date information on recent technological and policy changes
            - Next Step: Evaluate if GeneralKnowledgeEmployee needed for background context, then SummaryEmployee

            **Complex Query Example**: "How has artificial intelligence impacted job markets historically and what are current trends?"
            - Analysis: Multi-part query requiring both historical context and current information
            - Workflow Plan:
              1. GeneralKnowledgeEmployee: Historical impact of AI on job markets
              2. WebSearchEmployee: Current trends and recent developments
              3. SummaryEmployee: Synthesize historical and current information
            - Rationale: Sequential workflow ensures comprehensive coverage of both timeframes

            Remember: Your role is strategic coordination, not information provision. Focus on analyzing, planning, delegating, and managing workflows that result in comprehensive, high-quality responses to user queries.
            """

    def get_agent(self) -> Optional[AssistantAgent]:
        """
        Get the underlying AssistantAgent instance.

        Returns:
            Optional[AssistantAgent]: The agent instance if initialized, None otherwise
        """
        return self._agent if self._is_initialized else None

    def is_initialized(self) -> bool:
        """
        Check if the agent is properly initialized.

        Returns:
            bool: True if initialized, False otherwise
        """
        return self._is_initialized

    @classmethod
    async def create_and_initialize(
        cls,
        memory: Optional[ListMemory] = [],
        tools: Optional[List[str]] = None,
    ) -> Optional["OrchestratorEmployee"]:
        """
        Create and initialize an OrchestratorEmployee instance.

        Returns:
            Optional[OrchestratorEmployee]: Initialized agent instance or None if failed
        """
        agent = cls()
        if await agent.initialize(memory, tools):
            return agent
        else:
            logger.error("Failed to create and initialize OrchestratorEmployee")
            return None


async def build_agents(
    run_id: str,
    memory: Optional[ListMemory] = None,
    organisation_id: Optional[str] = None,
    user_id: Optional[str] = None,
    tools: Optional[List[str]] = None,
) -> list[AssistantAgent]:
    orchestrator_agent = await OrchestratorEmployee.create_and_initialize(
        memory=memory, tools=tools
    )

    user_proxy = UserProxyAgent(
        name="UserProxyAgent",
        description="This agent should be used when the user's input is required to make decisions or provide information to the OrchestratorEmployee.",
    )

    # ASK mode agents
    general_agent = await GeneralKnowledgeEmployee.create_and_initialize()
    web_search_agent = await WebSearchEmployee.create_and_initialize()
    summary_agent = await SummaryEmployee.create_and_initialize()
    knowledge_base_agent = await KnowledgeBaseEmployee.create_and_initialize(
        run_id=run_id,
        organisation_id=organisation_id,
        user_id=user_id,
    )

    # ACT mode agents
    discovery_agent_master = await DiscoveryMasterEmployee.create_and_initialize()

    return [
        orchestrator_agent.get_agent(),
        general_agent.get_agent(),
        web_search_agent.get_agent(),
        summary_agent.get_agent(),
        knowledge_base_agent.get_agent(),
        discovery_agent_master.get_agent(),
        user_proxy,
    ]


def build_selector_prompt() -> str:
    """Return the simplified selector prompt template."""

    return """
    Select an employee to perform task.

    {roles}

    Current conversation context:
    {history}

    Read the above conversation, then select an employee from {participants} to perform the next task.
    Make sure the OrchestratorEmployee has assigned tasks before other employees start working.
    Once the OrchestratorEmployee has responded, DO NOT select the OrchestratorEmployee again.
    Only select one employee.
    """


async def build_orchestration_team(
    run_id: str,
    memory: Optional[ListMemory] = [],
    organisation_id: Optional[str] = None,
    user_id: Optional[str] = None,
    tools: Optional[List[str]] = None,
) -> SelectorGroupChat:
    """Instantiate the orchestrator team object."""

    # Create model configuration
    model_config = {
        "llm_type": "openai",
        "provider": "OpenAIChatCompletionClient",
        "model": "o4-mini",
        "api_key": get_settings().requesty.api_key,
        "base_url": get_settings().requesty.base_url,
    }

    model_client = ModelFactory.create_model_client(model_config)

    agents = await build_agents(run_id, memory, organisation_id, user_id, tools)

    termination_condition = TextMentionTermination("TERMINATE")

    orchestrator = SelectorGroupChat(
        participants=agents,
        model_client=model_client,
        selector_prompt=build_selector_prompt(),
        # emit_team_events=True,
        model_client_streaming=True,
        termination_condition=termination_condition,
        selector_func=selector_func,
        allow_repeated_speaker=False,
    )

    return orchestrator


def selector_func(messages: Sequence[BaseAgentEvent | BaseChatMessage]) -> str:
    print(f"Last: {messages[-1].source} : {type(messages[-1].source)}")
    if messages[-1].source != "OrchestratorEmployee":
        return "OrchestratorEmployee"
