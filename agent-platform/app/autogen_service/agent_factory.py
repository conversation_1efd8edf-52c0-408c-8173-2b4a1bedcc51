import asyncio
import logging
from contextlib import suppress
from typing import Any, Dict, List, Optional, Tuple

from asgiref.sync import sync_to_async
from autogen_agentchat.agents import AssistantAgent, UserProxyAgent
from autogen_agentchat.conditions import MaxMessageTermination, TextMentionTermination
from autogen_agentchat.messages import TextMessage
from autogen_agentchat.teams import RoundRobinGroupChat, SelectorGroupChat
from autogen_core.memory import ListMemory, MemoryContent
from autogen_core.model_context import BufferedChatCompletionContext

from ..memory import memory_manager
from ..schemas.api import AgentConfig, Session
from ..shared.config.base import get_settings
from ..tools.knowledge_tool_loader import KnowledgeToolLoader
from ..tools.mcp_tool_loader import Mcp<PERSON>ool<PERSON>oader
from ..tools.workflow_tool_loader import WorkflowToolLoader
from ..utils.converter import to_valid_identifier
from .model_factory import ModelFactory

logger = logging.getLogger(__name__)

# Disable autogen and SSE logging
for logger_name in [
    # Autogen loggers
    "autogen_agentchat",
    "autogen_core",
    "_single_threaded_agent_runtime",
    "autogen_runtime_core",
    "autogen_agentchat.teams",
    "autogen_agentchat.agents",
    # SSE loggers
    "sse",
    "mcp.client.sse",
    "mcp.client.session",
    "autogen_ext.tools.mcp",
]:
    # Initialize the logger
    logger = logging.getLogger(logger_name)

    # Set the logger level to critical (only show critical errors)
    logger.setLevel(logging.CRITICAL)

    # Set the logger propagate to false (don't pass messages to parent loggers)
    logger.propagate = False


class AgentFactory:
    def __init__(self):
        self.settings = get_settings()
        self.logger = logger
        self._model_clients_cache = {}
        self._model_cache_lock = asyncio.Lock()

    async def initialize_chat_session(
        self,
        run_id: str,
        agent_config: AgentConfig,
        session_memory: Optional[ListMemory] = None,
        organization_id: Optional[str] = None,
        user_id: Optional[str] = None,
        use_knowledge: Optional[bool] = False,
        variables: Optional[dict] = None,
    ) -> Tuple[List[Any], Any, ListMemory]:
        """Initialize chat session with agents and context."""
        try:
            # Initialize or use existing memory
            memory = session_memory or ListMemory()

            # Create model context with memory
            model_context = BufferedChatCompletionContext(buffer_size=32)
            await memory.update_context(model_context)

            agent = await self.create_agent(
                run_id=run_id,
                agent_config=AgentConfig(**agent_config),
                model_context=model_context,
                memory=memory,
                organization_id=organization_id,
                use_knowledge=use_knowledge,
                variables=variables,
                user_id=user_id,
            )

            return agent

        except Exception as e:
            self.logger.error(f"Failed to initialize chat session: {e}")
            raise e

    async def create_direct_agent(
        self,
        agent_config: Dict[str, Any],
        run_id: str,
        organization_id: Optional[str] = None,
        use_knowledge: Optional[bool] = False,
        variables: Optional[dict] = None,
        user_id: Optional[str] = None,
    ) -> AssistantAgent:
        """
        Create a single agent directly without session management overhead.
        Optimized for single message processing scenarios.

        Args:
            agent_config: Agent configuration dictionary
            run_id: Unique identifier for this run
            organization_id: Organization identifier for knowledge access
            use_knowledge: Whether to enable knowledge tools
            variables: Variables for dynamic prompt substitution
            user_id: User identifier

        Returns:
            AssistantAgent: Ready-to-use agent instance
        """
        try:
            # Create model context without persistent memory
            model_context = BufferedChatCompletionContext(buffer_size=32)

            # Convert config dict to AgentConfig if needed
            if isinstance(agent_config, dict):
                agent_config_obj = AgentConfig(**agent_config)
            else:
                agent_config_obj = agent_config

            # Create agent directly
            agent = await self.create_agent(
                run_id=run_id,
                agent_config=agent_config_obj,
                model_context=model_context,
                memory=None,  # No persistent memory for single messages
                organization_id=organization_id,
                use_knowledge=use_knowledge,
                variables=variables,
                user_id=user_id,
                use_pinecone_memory=False,  # Disable for single message processing
            )

            self.logger.info(
                f"Direct agent created successfully: {agent_config_obj.name}"
            )
            return agent

        except Exception as e:
            self.logger.error(f"Failed to create direct agent: {e}")
            raise e

    async def create_agent(
        self,
        run_id: str,
        agent_config: AgentConfig,
        model_context: BufferedChatCompletionContext,
        memory: Optional[ListMemory] = None,
        organization_id: Optional[str] = None,
        use_knowledge: Optional[bool] = False,
        variables: Optional[dict] = None,
        user_id: Optional[str] = "123",
        use_pinecone_memory: bool = False,
    ) -> AssistantAgent:
        """Create agent with shared memory and context."""

        tools = []

        try:
            # Create chat completion client (with caching)
            chat_completion_client = await self._get_or_create_model_client(
                llm_type=agent_config.model_provider or "openai",
                llm_model=agent_config.model_name or "gpt-4o",
            )

            # Process variables in system message if provided
            system_message = agent_config.system_message
            if variables:
                system_message = self._process_variables(system_message, variables)

            # Create enhanced system prompt
            enhanced_system_prompt = (
                f"{system_message}\n\n"
                "IMPORTANT: Maintain context of the conversation. "
                "Remember previous messages and refer to them when appropriate. "
                "Be consistent with your previous responses.\n\n"
                "MULTIMODAL CAPABILITIES: You can process and analyze images, documents, and other file attachments. "
                "When users provide files, the content will be extracted and included in the message for your analysis. "
                "Look for sections marked with '--- Content from [filename] ---' which contain extracted text from documents. "
                "Analyze this extracted content thoroughly and provide detailed responses based on what you can read. "
                "For images, examine visual elements and describe what you see. "
                "Always acknowledge that you can see and analyze the provided content."
            )

            # Add tone instruction if tone is specified
            if agent_config.tone and agent_config.tone.strip():
                enhanced_system_prompt += (
                    f"\n\nTONE INSTRUCTION: Always respond in a "
                    f"{agent_config.tone} tone. "
                    f"Adapt your communication style, word choice, and manner "
                    f"of expression to consistently reflect a "
                    f"{agent_config.tone} "
                    f"personality throughout all your interactions."
                )

            # Add knowledge tool instructions if knowledge is enabled
            if use_knowledge and organization_id:
                enhanced_system_prompt += (
                    "\n\nKNOWLEDGE ACCESS: You have access to the "
                    "organization's knowledge base through the "
                    "'get_knowledge_content' tool. When users ask questions "
                    "that might benefit from organizational knowledge, "
                    "documents, or specific information, use the "
                    "get_knowledge_content tool to search for relevant "
                    "content. "
                    "Always provide comprehensive responses that include:\n"
                    "1. Direct answers based on the retrieved knowledge\n"
                    "2. Proper attribution with source references "
                    "(file names, links)\n"
                    "3. Relevance scores when available\n"
                    "4. Clear indication when information comes from the "
                    "knowledge base\n\n"
                    "When presenting knowledge-based responses:\n"
                    "- Format the information clearly and professionally\n"
                    "- Include source references for credibility\n"
                    "- Mention the document type (PDF, DOC, etc.) when relevant\n"
                    "- If multiple sources are found, synthesize the "
                    "information coherently\n"
                    "- If no relevant knowledge is found, clearly state this "
                    "and provide general assistance\n\n"
                    "Example response format:\n"
                    "Based on the knowledge base search, I found the "
                    "following information:\n\n"
                    "[Your synthesized answer here]\n\n"
                    "**Sources:**\n"
                    "- Document Name (Type: PDF/DOC) - Relevance: X.XX\n"
                    "- [Additional sources as needed]\n\n"
                    "Use the knowledge tool proactively when questions relate "
                    "to company policies, procedures, documentation, or "
                    "specific organizational information."
                )

            # Load tools concurrently
            workflow_tools, mcp_tools, knowledge_tools = await asyncio.gather(
                self._load_workflow_tools(run_id, user_id, agent_config.workflows),
                self._load_mcp_tools(run_id, user_id, agent_config.mcps),
                self._load_knowledge_tools(
                    run_id, organization_id, user_id, agent_config.id, use_knowledge
                ),
            )

            tools.extend(workflow_tools)
            tools.extend(mcp_tools)
            tools.extend(knowledge_tools)

            print(f"Loaded tools: {tools}")

            # Create memory based on configuration
            agent_memory = []
            if use_pinecone_memory:
                # Use Pinecone memory for persistent, searchable memory
                pinecone_memory = memory_manager.get_agent_memory(
                    agent_id=agent_config.id or agent_config.name,
                    user_id=user_id,
                    namespace=f"agent-{agent_config.id or agent_config.name}",
                )
                agent_memory.append(pinecone_memory)
                self.logger.info(f"Using Pinecone memory for agent {agent_config.name}")

            if memory:
                # Use provided ListMemory
                agent_memory.append(memory)
                self.logger.info(f"Using ListMemory for agent {agent_config.name}")

            # Create assistant agent & add to the list
            agent_slug = to_valid_identifier(agent_config.name)

            # Create agent with shared memory and context
            agent = AssistantAgent(
                name=agent_slug,
                description=agent_config.description,
                model_client=chat_completion_client,
                system_message=enhanced_system_prompt,
                model_context=model_context,
                memory=agent_memory if agent_memory else [],
                tools=tools,
                reflect_on_tool_use=True,
                model_client_stream=True,
            )

            return agent

        except Exception as e:
            self.logger.error(f"Failed to create agent: {e}")
            raise

    # Setup agent memory
    async def setup_agent_memory(
        agent_id: int,
        previous_messages: list[dict[str, Any]],
    ) -> tuple[ListMemory, BufferedChatCompletionContext]:
        """Set up memory and context for an agent.

        Args:
            agent_id (int): The ID of the agent.
            previous_messages (list[dict[str, Any]]): Previous messages to add to memory.

        Returns:
            tuple[ListMemory, BufferedChatCompletionContext]: The memory and context objects.
        """

        # Create a memory for the agent
        agent_memory = ListMemory(name=f"agent_{agent_id}_memory")

        # Create a model context with memory
        model_context = BufferedChatCompletionContext(buffer_size=32)

        # Add previous messages to memory
        added_messages = 0
        for msg in previous_messages:
            # If the message is a system message or doesn't have content
            if not msg.get("content") or msg.get("sender") not in ("user", "agent"):
                # Skip the message
                continue

            with suppress(ValueError, TypeError, AttributeError):
                # Create a memory content from the message
                content = f"{msg.get('sender')}: {msg.get('content')}"
                memory_content = MemoryContent(
                    content=content,
                    mime_type="text/plain",
                )

                # Add to memory
                await agent_memory.add(memory_content)

                # Increment the counter
                added_messages += 1

        # Update the model context with memory
        with suppress(Exception):
            await agent_memory.update_context(model_context)

        # Return the memory and context
        return agent_memory, model_context

    async def _get_or_create_model_client(
        self,
        llm_type: str = "openai",
        llm_model: str = "gpt-4o",
        llm_max_tokens: int | None = None,
    ) -> Any:
        """
        Creates or retrieves a chat completion client based on the provided parameters.

        Args:
            llm_type: The type of LLM (e.g., "openai", "anthropic")
            llm_model: The model name
            llm_api_key: The API key
            llm_max_tokens: Maximum tokens (optional)

        Returns:
            A chat completion client instance
        """

        # Map llm_type to provider
        provider_map = {
            "openai": "OpenAIChatCompletionClient",
            "anthropic": "AnthropicChatCompletionClient",
        }

        provider = provider_map.get(llm_type.lower(), "OpenAIChatCompletionClient")

        # Create model configuration
        model_config = {
            "llm_type": llm_type.lower(),
            "provider": provider,
            "model": llm_model or "gpt-4o",
            "api_key": self.settings.requesty.api_key,
            "base_url": self.settings.requesty.base_url,
        }

        # Add max_tokens if provided
        if llm_max_tokens:
            model_config["max_tokens"] = llm_max_tokens

        # Use ModelFactory to create the model client
        return ModelFactory.create_model_client(model_config)

    async def _load_workflow_tools(
        self, run_id: str, user_id: str, workflows: Optional[List[str]]
    ) -> List[Any]:
        if not workflows:
            return []
        workflow_loader = WorkflowToolLoader()
        return await workflow_loader.load_workflows_as_tools(run_id, user_id, workflows)

    async def _load_mcp_tools(
        self, run_id, user_id, mcp_servers: Optional[List[str]]
    ) -> List[Any]:
        if not mcp_servers:
            return []
        mcp_loader = McpToolLoader()
        return await mcp_loader.load_mcps_as_tools(run_id, user_id, mcp_servers)

    # Setup Autogen agents
    async def setup_autogen_agents(
        self, agents: list[AgentConfig], previous_messages: list[dict[str, Any]]
    ) -> list[Any]:
        """Set up Autogen agents from Django Agent models.

        Args:
            agents (list[Agent]): List of Agent models.
            previous_messages (list[dict[str, Any]]): Previous messages for context.

        Returns:
            list[Any]: List of Autogen agent instances.
        """

        # List of Autogen agents
        autogen_agents = []

        # Traverse the agents
        for agent in agents:
            with suppress(Exception):
                # Get the agent details
                agent_name = agent.name
                agent_description = agent.description
                agent_system_prompt = agent.system_prompt

                # Get the LLM details from database
                llm_details = await self.get_llm_details(agent.id)

                # If the LLM details are not found
                if not llm_details:
                    # Skip this iteration
                    continue

                # Extract LLM details
                llm_type = llm_details["api_type"]
                llm_model = llm_details["model"]
                llm_max_tokens = llm_details["max_tokens"]
                llm_api_key = llm_details["api_key"]

                # Create the chat completion client
                chat_completion_client = self.create_chat_completion_client(
                    llm_type, llm_model, llm_api_key, llm_max_tokens
                )

                # If the chat completion client is not created
                if not chat_completion_client:
                    # Skip this iteration
                    continue

                # Set up memory and context
                agent_memory, model_context = await setup_agent_memory(
                    agent.id, previous_messages
                )

                # Get MCP tools for the agent
                mcp_tools = await get_mcp_tools_for_agent(agent.id)

                # Enhance the system prompt with instructions to maintain context
                enhanced_system_prompt = (
                    f"{agent_system_prompt}\n\n"
                    "IMPORTANT: Maintain context of the conversation. "
                    "Remember previous messages and refer to them when appropriate. "
                    "Be consistent with your previous responses."
                )

                # If there are MCP tools
                if mcp_tools:
                    # Add information about available tools
                    enhanced_system_prompt += "\n\nYou have access to external tools. Use them when appropriate to fulfill user requests."

                # Create assistant agent & add to the list
                agent_slug = to_valid_identifier(agent_name)

                # Initialize the assistant agent with tools if available
                assistant_agent = AssistantAgent(
                    name=agent_slug,
                    description=agent_description,
                    model_client=chat_completion_client,
                    system_message=enhanced_system_prompt,
                    model_context=model_context,
                    tools=mcp_tools if mcp_tools else None,
                    reflect_on_tool_use=True,
                )

                # Add the agent to the list
                autogen_agents.append(assistant_agent)

        # Create user proxy agent
        user_proxy = UserProxyAgent("user")

        # Add the user proxy agent to the autogen agents
        autogen_agents.append(user_proxy)

        # Return the list of agents
        return autogen_agents

    async def _create_chat_team(
        self,
        chat_type: str,
        autogen_agents: list[AgentConfig],
        session: Session,
        agents: list[AgentConfig],
    ):
        """Create a chat team based on chat type.

        Args:
            chat_type (str): The type of chat (single, group).
            autogen_agents (list[Agent]): List of autogen agent instances.
            session (Session): The session object containing LLM and other settings.
            agents (list[Agent]): List of agent models.

        Returns:
            tuple[Any, list[tuple[int, str, str]] | None]: The team object and error response (if any).
        """

        # Set the termination conditions
        text_mention_termination = TextMentionTermination("TERMINATE")
        max_messages_termination = MaxMessageTermination(max_messages=8)
        termination = text_mention_termination | max_messages_termination

        # If the chat type is single
        if chat_type == "single":
            # Create round robin group chat
            return RoundRobinGroupChat(
                participants=autogen_agents, termination_condition=termination
            )

        # If chat type is group
        if chat_type == "group":
            try:
                # Get session llm details
                llm_details = await self.get_llm_details_by_llm_id(session.llm.id)

                # Initialize the model client
                model_client = self.create_chat_completion_client(
                    llm_details["api_type"],
                    llm_details["model"],
                    llm_details["api_key"],
                    llm_details["max_tokens"],
                )

                # Create the selector group chat using sync_to_async
                create_selector_chat = sync_to_async(SelectorGroupChat)
                team = await create_selector_chat(
                    participants=autogen_agents,
                    model_client=model_client,
                    termination_condition=termination,
                )

            except (ValueError, TypeError, AttributeError) as e:
                # If agents in list
                if agents:
                    # Return the error
                    return None, [
                        (
                            agents[0].id,
                            "agent",
                            f"I encountered an error setting up the group chat: {e!s}",
                        )
                    ]

                # Return an empty list
                return None, []

            # Return the team & error
            return team, None

        # Invalid chat type
        return None, []

    # Process the user message with the agents and get the response
    async def process_with_agents(  # noqa: PLR0911
        self,
        chat_type: str,
        session: Session,
        user_message: str,
        agents: list[AgentConfig],
        params: dict[str, Any] | None = None,
    ) -> list[tuple[int, str, str]]:
        """Process the user message with the agents and get the response.

        Args:
            chat_type (str): The type of chat (single, group).
            session (Session): The session object containing LLM and other settings.
            user_message (str): The user message to process.
            agents (list[Agent]): List of agent models.
            params (Optional[Dict[str, Any]], optional): Additional parameters including:
                - previous_messages (list[dict[str, Any]]): Previous messages for context.
                - message_callback (callable): Callback for processing each message as it arrives.

        Returns:
            list[tuple[int, str, str]]: A list of tuples containing the agent ID, source, and the response content.
        """

        # Extract parameters from params dict
        params = params or {}
        previous_messages = params.get("previous_messages", [])
        message_callback = params.get("message_callback", None)

        # Default response for empty agents or errors
        default_response = []

        # If agents in list
        if agents:
            # Set default response
            default_response = [
                (
                    agents[0].id,
                    "agent",
                    "I'm sorry, I couldn't process your request at this time.",
                )
            ]

        try:
            # Set up Autogen agents
            autogen_agents = await self.setup_autogen_agents(agents, previous_messages)

            # If no valid agents were created
            if len(autogen_agents) <= 1:
                # Return default response
                return default_response if agents else []

            # Create team based on chat type
            team, error_response = await self._create_chat_team(
                chat_type, autogen_agents, session, agents
            )

            # If there was an error creating the team
            if error_response:
                # Return error response
                return error_response

            # If team was not created
            if not team:
                # Return empty list
                return []

            # Create the message
            message = TextMessage(content=user_message, source="user")

            # Start the stream
            stream = team.run_stream(task=message)

            # Process the message stream
            agent_responses = await self._process_message_stream(
                stream, agents, message_callback
            )

            # If no responses were collected but we have agents
            if not agent_responses and agents:
                # Return the default response
                return default_response

        except (ValueError, TypeError, AttributeError) as e:
            # If we have agents
            if agents:
                # Return the default message
                return [
                    (
                        agents[0].id,
                        "agent",
                        f"I encountered an error while processing your request: {e!s}",
                    )
                ]

            # Return empty list
            return []

        # Return agent response
        return agent_responses

    async def _load_knowledge_tools(
        self,
        run_id: str,
        organization_id: Optional[str],
        user_id: Optional[str],
        agent_id: Optional[str],
        use_knowledge: Optional[bool] = False,
    ) -> List[Any]:
        """Load knowledge tools if use_knowledge is True and organization_id is provided."""
        if not use_knowledge or not organization_id:
            return []

        try:

            knowledge_loader = KnowledgeToolLoader()
            all_tools = []

            # Always add the knowledge content retrieval tools
            content_tools = knowledge_loader.create_knowledge_content_tools(
                run_id=run_id,
                organization_id=organization_id,
                user_id=user_id,
                agent_id=agent_id,
            )
            all_tools.extend(content_tools)
            self.logger.info(f"Added {len(content_tools)} knowledge content tools")

            self.logger.info(
                f"Total knowledge tools loaded for organization "
                f"{organization_id}: {len(all_tools)}"
            )
            return all_tools

        except Exception as e:
            self.logger.error(
                f"Failed to load knowledge tools for organization "
                f"{organization_id}: {e}"
            )
            return []

    def _process_variables(self, text: str, variables: dict) -> str:
        """Process variables in text by replacing {{variable}} with values from variables dict."""
        if not text or not variables:
            return text

        import re

        # Find all variables in the format {{variable}}
        pattern = r"\{\{(\w+)\}\}"

        def replace_variable(match):
            var_name = match.group(1)
            if var_name in variables:
                return str(variables[var_name])
            else:
                # Keep the original placeholder if variable not found
                self.logger.warning(
                    f"Variable '{var_name}' not found in variables dict"
                )
                return match.group(0)

        # Replace all variables
        processed_text = re.sub(pattern, replace_variable, text)

        self.logger.debug(
            f"Processed variables in text. Original length: {len(text)}, "
            f"Processed length: {len(processed_text)}"
        )

        return processed_text
        return processed_text
